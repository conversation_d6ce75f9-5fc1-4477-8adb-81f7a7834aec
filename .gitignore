# ==================== 通用忽略文件 ====================

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# ==================== Java/Maven 项目 ====================

# Maven 编译输出
code-fuel-backend/target/
code-fuel-backend/.mvn/wrapper/maven-wrapper.jar

# Java 编译文件
*.class
*.jar
*.war
*.ear
*.nar

# JVM 崩溃日志
hs_err_pid*

# ==================== Node.js/Vue 项目 ====================

# 依赖目录
code-fuel-frontend/node_modules/
node_modules/

# 构建输出
code-fuel-frontend/dist/
code-fuel-frontend/build/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# npm 调试日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ==================== 数据库和配置 ====================

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
application-prod.yml
application-dev.yml

# ==================== 用户上传文件 ====================

# 用户头像和文件上传
code-fuel-backend/file/image/
uploads/

# ==================== Python 项目 ====================

# Python 缓存
__pycache__/
*.py[cod]
*$py.class

# 虚拟环境
venv/
env/
ENV/

# ==================== 其他 ====================

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 文档生成
docs/_build/
