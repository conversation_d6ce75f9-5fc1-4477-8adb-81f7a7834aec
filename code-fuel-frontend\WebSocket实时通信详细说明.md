# WebSocket 实时通信系统详细说明

## 概述

CodeDuel 项目采用 WebSocket + STOMP 协议实现房间内的实时通信，确保所有用户能够同步接收房间状态变化、用户加入/离开、对战开始/结束等消息。

## 技术架构

### 后端架构 (Spring Boot + WebSocket)

#### 1. WebSocket 配置
**文件**: `src/main/java/com/xju/codeduel/config/WebSocketConfig.java`

```java
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {
    
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // 启用简单消息代理
        config.enableSimpleBroker("/topic", "/queue");
        // 设置应用程序消息前缀
        config.setApplicationDestinationPrefixes("/app");
        // 设置用户目标前缀
        config.setUserDestinationPrefix("/user");
    }
    
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 注册WebSocket端点
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*")
                .withSockJS();
    }
}
```

#### 2. 消息路由设计

| 路由类型   | 路径模式    | 用途                   | 示例                    |
| ---------- | ----------- | ---------------------- | ----------------------- |
| 客户端发送 | `/app/**`   | 客户端向服务端发送消息 | `/app/room/123456/join` |
| 广播消息   | `/topic/**` | 服务端向多个客户端广播 | `/topic/room/123456`    |
| 点对点消息 | `/queue/**` | 服务端向特定用户发送   | `/queue/user/123`       |

#### 3. 房间消息处理器
**文件**: `src/main/java/com/xju/codeduel/websocket/RoomWebSocketHandler.java`

```java
@Controller
public class RoomWebSocketHandler {
    
    // 处理用户加入房间
    @MessageMapping("/room/{roomCode}/join")
    @SendTo("/topic/room/{roomCode}")
    public RoomUpdateMessage handleJoinRoom(
            @DestinationVariable Long roomCode,
            Map<String, Object> message) {
        // 处理加入逻辑，返回房间更新消息
    }
    
    // 处理用户离开房间
    @MessageMapping("/room/{roomCode}/leave")
    @SendTo("/topic/room/{roomCode}")
    public RoomUpdateMessage handleLeaveRoom(
            @DestinationVariable Long roomCode,
            Map<String, Object> message) {
        // 处理离开逻辑，返回房间更新消息
    }
    
    // 处理对战开始
    @MessageMapping("/room/{roomCode}/start")
    @SendTo("/topic/room/{roomCode}")
    public RoomUpdateMessage handleStartBattle(
            @DestinationVariable Long roomCode,
            Map<String, Object> message) {
        // 处理对战开始逻辑
    }
    
    // 主动广播房间状态更新
    public void broadcastRoomUpdate(Long roomCode, RoomUpdateMessage updateMessage) {
        messagingTemplate.convertAndSend("/topic/room/" + roomCode, updateMessage);
    }
}
```

#### 4. 消息数据结构

```java
public static class RoomUpdateMessage {
    private String type;           // 消息类型：USER_JOINED, USER_LEFT, BATTLE_STARTED, etc.
    private RoomInfoDTO roomInfo;  // 最新房间信息
    private String message;        // 描述消息
    private Long timestamp;        // 时间戳
}
```

## 前端 WebSocket 集成方案

### 1. 安装依赖

首先需要安装 WebSocket 相关的前端库：

```bash
npm install @stomp/stompjs sockjs-client
```

### 2. WebSocket 服务封装

**文件**: `src/utils/websocket.js`

```javascript
import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client'

class WebSocketService {
  constructor() {
    this.client = null
    this.connected = false
    this.subscriptions = new Map()
  }

  // 连接WebSocket
  connect() {
    return new Promise((resolve, reject) => {
      this.client = new Client({
        // 使用SockJS作为传输层
        webSocketFactory: () => new SockJS('http://localhost:8080/ws'),
        
        // 连接成功回调
        onConnect: (frame) => {
          console.log('✅ WebSocket连接成功:', frame)
          this.connected = true
          resolve(frame)
        },
        
        // 连接失败回调
        onStompError: (frame) => {
          console.error('❌ WebSocket连接失败:', frame)
          this.connected = false
          reject(frame)
        },
        
        // 断开连接回调
        onDisconnect: () => {
          console.log('🔌 WebSocket连接断开')
          this.connected = false
        },
        
        // 心跳设置
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        
        // 重连设置
        reconnectDelay: 5000,
        
        // 调试模式
        debug: (str) => {
          console.log('🔍 WebSocket调试:', str)
        }
      })
      
      this.client.activate()
    })
  }

  // 断开连接
  disconnect() {
    if (this.client && this.connected) {
      this.client.deactivate()
      this.subscriptions.clear()
    }
  }

  // 订阅房间消息
  subscribeToRoom(roomCode, callback) {
    if (!this.connected) {
      console.error('❌ WebSocket未连接，无法订阅房间消息')
      return null
    }

    const destination = `/topic/room/${roomCode}`
    
    const subscription = this.client.subscribe(destination, (message) => {
      try {
        const data = JSON.parse(message.body)
        console.log('📨 收到房间消息:', data)
        callback(data)
      } catch (error) {
        console.error('❌ 解析房间消息失败:', error)
      }
    })

    this.subscriptions.set(roomCode, subscription)
    console.log(`📡 已订阅房间 ${roomCode} 的消息`)
    
    return subscription
  }

  // 取消订阅房间消息
  unsubscribeFromRoom(roomCode) {
    const subscription = this.subscriptions.get(roomCode)
    if (subscription) {
      subscription.unsubscribe()
      this.subscriptions.delete(roomCode)
      console.log(`📡 已取消订阅房间 ${roomCode} 的消息`)
    }
  }

  // 发送加入房间消息
  sendJoinRoom(roomCode, userInfo) {
    if (!this.connected) {
      console.error('❌ WebSocket未连接，无法发送消息')
      return
    }

    this.client.publish({
      destination: `/app/room/${roomCode}/join`,
      body: JSON.stringify({
        userId: userInfo.id,
        userName: userInfo.codeforcesId
      })
    })
  }

  // 发送离开房间消息
  sendLeaveRoom(roomCode, userInfo) {
    if (!this.connected) {
      console.error('❌ WebSocket未连接，无法发送消息')
      return
    }

    this.client.publish({
      destination: `/app/room/${roomCode}/leave`,
      body: JSON.stringify({
        userId: userInfo.id,
        userName: userInfo.codeforcesId
      })
    })
  }

  // 发送开始对战消息
  sendStartBattle(roomCode, userInfo) {
    if (!this.connected) {
      console.error('❌ WebSocket未连接，无法发送消息')
      return
    }

    this.client.publish({
      destination: `/app/room/${roomCode}/start`,
      body: JSON.stringify({
        userId: userInfo.id,
        userName: userInfo.codeforcesId
      })
    })
  }

  // 检查连接状态
  isConnected() {
    return this.connected
  }
}

// 创建全局WebSocket服务实例
export const webSocketService = new WebSocketService()
export default WebSocketService
```

### 3. 在组件中使用 WebSocket

#### 房间等待组件集成 WebSocket
**文件**: `src/components/RoomWaiting.vue`

```javascript
import { webSocketService } from '@/utils/websocket'

export default {
  setup() {
    // ... 其他代码

    // WebSocket连接和订阅
    const connectWebSocket = async () => {
      try {
        if (!webSocketService.isConnected()) {
          await webSocketService.connect()
        }
        
        // 订阅房间消息
        webSocketService.subscribeToRoom(localRoomInfo.value.roomCode, handleRoomMessage)
        
        // 发送加入房间消息
        webSocketService.sendJoinRoom(localRoomInfo.value.roomCode, currentUser.value)
        
      } catch (error) {
        console.error('❌ WebSocket连接失败:', error)
        ElMessage.error('实时通信连接失败，将使用轮询模式')
      }
    }

    // 处理房间消息
    const handleRoomMessage = (message) => {
      console.log('📨 收到房间消息:', message)
      
      switch (message.type) {
        case 'USER_JOINED':
          ElMessage.success(message.message)
          localRoomInfo.value = message.roomInfo
          emit('room-update', message.roomInfo)
          break
          
        case 'USER_LEFT':
          ElMessage.info(message.message)
          localRoomInfo.value = message.roomInfo
          emit('room-update', message.roomInfo)
          break
          
        case 'BATTLE_STARTED':
          ElMessage.success(message.message)
          localRoomInfo.value = message.roomInfo
          emit('battle-start', message.roomInfo)
          break
          
        default:
          console.log('未知消息类型:', message.type)
      }
    }

    // 组件挂载时连接WebSocket
    onMounted(() => {
      connectWebSocket()
      // 保留原有的轮询作为备用方案
      refreshTimer = setInterval(refreshRoomInfo, 10000) // 降低轮询频率到10秒
    })

    // 组件卸载时断开连接
    onUnmounted(() => {
      if (localRoomInfo.value?.roomCode) {
        webSocketService.unsubscribeFromRoom(localRoomInfo.value.roomCode)
        webSocketService.sendLeaveRoom(localRoomInfo.value.roomCode, currentUser.value)
      }
      
      if (refreshTimer) {
        clearInterval(refreshTimer)
      }
    })

    // 开始对战时发送WebSocket消息
    const handleStartBattle = async () => {
      if (!canStartBattle.value) {
        ElMessage.warning('当前无法开始对战')
        return
      }
      
      startingBattle.value = true
      
      try {
        // 通过WebSocket发送开始对战消息
        webSocketService.sendStartBattle(localRoomInfo.value.roomCode, currentUser.value)
        
        // 同时调用HTTP API作为备用
        const response = await startBattle(localRoomInfo.value.roomCode)
        
        if (response.status && response.data) {
          ElMessage.success('对战开始！')
          localRoomInfo.value = response.data
          emit('battle-start', response.data)
        } else {
          ElMessage.error(response.message || '开始对战失败')
        }
        
      } catch (error) {
        console.error('❌ 开始对战失败:', error)
        ElMessage.error('开始对战失败: ' + error.message)
      } finally {
        startingBattle.value = false
      }
    }

    return {
      // ... 其他返回值
      handleStartBattle
    }
  }
}
```

## WebSocket 消息流程详解

### 1. 房间生命周期中的 WebSocket 通信

#### 创建房间流程
```mermaid
sequenceDiagram
    participant User1 as 用户1(房主)
    participant Frontend1 as 前端1
    participant Backend as 后端
    participant WebSocket as WebSocket服务

    User1->>Frontend1: 点击创建房间
    Frontend1->>Backend: HTTP POST /api/room/create
    Backend-->>Frontend1: 返回房间信息
    Frontend1->>WebSocket: 连接WebSocket
    Frontend1->>WebSocket: 订阅 /topic/room/{roomCode}
    Frontend1->>WebSocket: 发送 /app/room/{roomCode}/join
    WebSocket-->>Frontend1: 广播房间状态更新
```

#### 用户加入房间流程
```mermaid
sequenceDiagram
    participant User2 as 用户2
    participant Frontend2 as 前端2
    participant Backend as 后端
    participant WebSocket as WebSocket服务
    participant Frontend1 as 前端1(房主)

    User2->>Frontend2: 输入房间码加入
    Frontend2->>Backend: HTTP POST /api/room/join
    Backend-->>Frontend2: 返回房间信息
    Frontend2->>WebSocket: 连接WebSocket
    Frontend2->>WebSocket: 订阅 /topic/room/{roomCode}
    Frontend2->>WebSocket: 发送 /app/room/{roomCode}/join
    WebSocket-->>Frontend1: 广播用户加入消息
    WebSocket-->>Frontend2: 广播用户加入消息
```

#### 开始对战流程
```mermaid
sequenceDiagram
    participant User1 as 用户1(房主)
    participant Frontend1 as 前端1
    participant Backend as 后端
    participant WebSocket as WebSocket服务
    participant Frontend2 as 前端2

    User1->>Frontend1: 点击开始对战
    Frontend1->>WebSocket: 发送 /app/room/{roomCode}/start
    WebSocket->>Backend: 调用 roomService.startBattle()
    Backend-->>WebSocket: 返回对战房间信息
    WebSocket-->>Frontend1: 广播对战开始消息
    WebSocket-->>Frontend2: 广播对战开始消息
    Frontend1->>Frontend1: 切换到对战界面
    Frontend2->>Frontend2: 切换到对战界面
```

### 2. 消息类型定义

#### 房间消息类型
```javascript
const MESSAGE_TYPES = {
  // 用户操作
  USER_JOINED: 'USER_JOINED',           // 用户加入房间
  USER_LEFT: 'USER_LEFT',               // 用户离开房间

  // 房间状态
  ROOM_STATUS_CHANGED: 'ROOM_STATUS_CHANGED', // 房间状态变化
  ROOM_DISSOLVED: 'ROOM_DISSOLVED',     // 房间解散

  // 对战相关
  BATTLE_STARTED: 'BATTLE_STARTED',     // 对战开始
  BATTLE_ENDED: 'BATTLE_ENDED',         // 对战结束

  // 系统消息
  SYSTEM_MESSAGE: 'SYSTEM_MESSAGE',     // 系统通知
  ERROR_MESSAGE: 'ERROR_MESSAGE'        // 错误消息
}
```

#### 消息数据结构
```javascript
// 房间更新消息
interface RoomUpdateMessage {
  type: string;           // 消息类型
  roomInfo: RoomInfo;     // 最新房间信息
  message: string;        // 描述消息
  timestamp: number;      // 时间戳
  data?: any;            // 额外数据
}

// 房间信息结构
interface RoomInfo {
  roomCode: number;       // 房间码
  status: string;         // 房间状态
  creator: User;          // 房主信息
  participants: User[];   // 参与者列表
  participantCount: number; // 参与者数量
  maxParticipants: number;  // 最大参与者数量
  problem?: Problem;      // 题目信息
  createTime: string;     // 创建时间
  description?: string;   // 房间描述
}
```

### 3. 错误处理和重连机制

#### WebSocket 连接状态管理
```javascript
// 在 websocket.js 中添加状态管理
class WebSocketService {
  constructor() {
    this.client = null
    this.connected = false
    this.connecting = false
    this.subscriptions = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 5000
  }

  // 自动重连机制
  async reconnect() {
    if (this.connecting || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return
    }

    this.reconnectAttempts++
    this.connecting = true

    console.log(`🔄 尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    try {
      await this.connect()
      this.reconnectAttempts = 0
      console.log('✅ WebSocket重连成功')
    } catch (error) {
      console.error('❌ WebSocket重连失败:', error)

      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        setTimeout(() => {
          this.reconnect()
        }, this.reconnectDelay * this.reconnectAttempts)
      } else {
        console.error('❌ WebSocket重连次数已达上限，停止重连')
      }
    } finally {
      this.connecting = false
    }
  }

  // 连接状态检查
  checkConnection() {
    if (!this.connected) {
      console.warn('⚠️ WebSocket连接已断开，尝试重连')
      this.reconnect()
      return false
    }
    return true
  }

  // 发送消息前检查连接
  sendMessage(destination, body) {
    if (!this.checkConnection()) {
      console.error('❌ WebSocket未连接，消息发送失败')
      return false
    }

    try {
      this.client.publish({
        destination,
        body: JSON.stringify(body)
      })
      return true
    } catch (error) {
      console.error('❌ 发送WebSocket消息失败:', error)
      return false
    }
  }
}
```

#### 降级方案：HTTP轮询
```javascript
// 在组件中实现WebSocket + HTTP轮询的混合方案
const useRoomSync = (roomCode) => {
  const wsConnected = ref(false)
  const pollingInterval = ref(null)

  // WebSocket连接成功后停止轮询
  const onWebSocketConnected = () => {
    wsConnected.value = true
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value)
      pollingInterval.value = null
    }
    console.log('✅ WebSocket连接成功，停止HTTP轮询')
  }

  // WebSocket断开后启动轮询
  const onWebSocketDisconnected = () => {
    wsConnected.value = false
    startPolling()
    console.log('⚠️ WebSocket连接断开，启动HTTP轮询')
  }

  // 启动HTTP轮询
  const startPolling = () => {
    if (pollingInterval.value) return

    pollingInterval.value = setInterval(async () => {
      try {
        const response = await getRoomInfo(roomCode)
        if (response.status) {
          // 处理房间信息更新
          handleRoomUpdate(response.data)
        }
      } catch (error) {
        console.error('❌ 轮询获取房间信息失败:', error)
      }
    }, 3000) // 3秒轮询一次
  }

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value)
      pollingInterval.value = null
    }
  }

  return {
    wsConnected,
    onWebSocketConnected,
    onWebSocketDisconnected,
    startPolling,
    stopPolling
  }
}
```

### 4. 性能优化策略

#### 消息去重和防抖
```javascript
// 消息去重机制
class MessageDeduplicator {
  constructor(windowSize = 1000) {
    this.messageIds = new Set()
    this.windowSize = windowSize
  }

  isDuplicate(messageId) {
    if (this.messageIds.has(messageId)) {
      return true
    }

    this.messageIds.add(messageId)

    // 限制内存使用，定期清理旧消息ID
    if (this.messageIds.size > this.windowSize) {
      const oldIds = Array.from(this.messageIds).slice(0, this.windowSize / 2)
      oldIds.forEach(id => this.messageIds.delete(id))
    }

    return false
  }
}

// 在WebSocket服务中使用
const messageDeduplicator = new MessageDeduplicator()

const handleMessage = (message) => {
  const messageId = `${message.type}_${message.timestamp}_${message.roomInfo?.roomCode}`

  if (messageDeduplicator.isDuplicate(messageId)) {
    console.log('🔄 忽略重复消息:', messageId)
    return
  }

  // 处理消息
  processMessage(message)
}
```

#### 连接池管理
```javascript
// WebSocket连接池
class WebSocketPool {
  constructor() {
    this.connections = new Map()
    this.maxConnections = 5
  }

  getConnection(key) {
    if (!this.connections.has(key)) {
      if (this.connections.size >= this.maxConnections) {
        // 移除最旧的连接
        const oldestKey = this.connections.keys().next().value
        this.removeConnection(oldestKey)
      }

      this.connections.set(key, new WebSocketService())
    }

    return this.connections.get(key)
  }

  removeConnection(key) {
    const connection = this.connections.get(key)
    if (connection) {
      connection.disconnect()
      this.connections.delete(key)
    }
  }
}
```

## 实际应用场景

### 1. 房间状态实时同步
- **用户加入/离开**: 实时更新参与者列表
- **房间状态变化**: WAITING → READY → BATTLING → FINISHED
- **房主权限**: 开始对战、踢出用户等操作

### 2. 对战过程实时通信
- **对战开始通知**: 所有用户同时收到开始信号
- **提交状态同步**: 实时显示对手提交情况
- **对战结果广播**: 胜负结果实时通知

### 3. 系统通知和错误处理
- **连接状态提示**: 显示WebSocket连接状态
- **网络异常处理**: 自动重连和降级方案
- **消息确认机制**: 确保重要消息送达

## 总结

通过WebSocket + STOMP协议的实时通信系统，CodeDuel项目实现了：

1. **真正的实时性**: 毫秒级的消息传递，用户操作立即同步
2. **高可靠性**: 自动重连、消息去重、降级方案确保系统稳定
3. **良好的用户体验**: 实时反馈、状态同步、无需手动刷新
4. **可扩展性**: 支持多房间、多用户、多种消息类型

相比原来的HTTP轮询方案，WebSocket方案具有更低的延迟、更少的服务器压力和更好的用户体验。

## 前端集成步骤

### 1. 安装依赖

```bash
npm install @stomp/stompjs sockjs-client
```

### 2. 配置环境变量

在 `.env` 文件中添加WebSocket服务器地址：

```env
# 开发环境
VITE_WS_URL=http://localhost:8080

# 生产环境
VITE_WS_URL=https://your-domain.com
```

### 3. 集成WebSocket服务

将提供的 `src/utils/websocket.js` 文件添加到项目中，该文件包含：

- **WebSocketService类**: 完整的WebSocket连接管理
- **自动重连机制**: 网络断开时自动重连
- **消息去重**: 防止重复消息处理
- **订阅管理**: 房间消息的订阅和取消订阅

### 4. 在组件中使用WebSocket

#### 基本使用方式

```javascript
import { webSocketService } from '@/utils/websocket'

// 在组件的 setup() 函数中
const connectWebSocket = async () => {
  try {
    // 连接WebSocket
    await webSocketService.connect()

    // 订阅房间消息
    webSocketService.subscribeToRoom(roomCode, (message) => {
      console.log('收到消息:', message)
      handleRoomMessage(message)
    })

    // 发送加入房间消息
    webSocketService.sendJoinRoom(roomCode, userInfo)

  } catch (error) {
    console.error('WebSocket连接失败:', error)
    // 启动HTTP轮询作为备用方案
    startHttpPolling()
  }
}
```

#### 完整的组件集成示例

参考提供的 `src/components/RoomWaitingWithWebSocket.vue` 文件，该组件展示了：

- **WebSocket连接状态显示**: 实时显示连接状态
- **消息实时处理**: 处理各种房间消息类型
- **降级方案**: WebSocket断开时自动切换到HTTP轮询
- **用户体验优化**: 连接状态提示、实时消息显示

### 5. 修改现有组件

#### 更新 RoomWaiting.vue

```javascript
// 在现有的 RoomWaiting.vue 中添加WebSocket支持
import { webSocketService } from '@/utils/websocket'

// 在 onMounted 中添加
onMounted(async () => {
  // 尝试连接WebSocket
  try {
    await webSocketService.connect()
    webSocketService.subscribeToRoom(localRoomInfo.value.roomCode, handleWebSocketMessage)
    webSocketService.sendJoinRoom(localRoomInfo.value.roomCode, currentUser.value)

    // WebSocket连接成功，降低轮询频率
    refreshTimer = setInterval(refreshRoomInfo, 10000) // 10秒
  } catch (error) {
    // WebSocket连接失败，使用原有轮询频率
    refreshTimer = setInterval(refreshRoomInfo, 3000) // 3秒
  }
})

// 添加WebSocket消息处理
const handleWebSocketMessage = (message) => {
  switch (message.type) {
    case 'USER_JOINED':
    case 'USER_LEFT':
      localRoomInfo.value = message.roomInfo
      emit('room-update', message.roomInfo)
      break
    case 'BATTLE_STARTED':
      localRoomInfo.value = message.roomInfo
      emit('battle-start', message.roomInfo)
      break
  }
}
```

#### 更新 BattleRoom.vue

```javascript
// 在对战组件中添加实时状态同步
import { webSocketService } from '@/utils/websocket'

// 订阅对战相关消息
const subscribeToRoomUpdates = () => {
  if (webSocketService.isConnected()) {
    webSocketService.subscribeToRoom(props.roomInfo.roomCode, (message) => {
      if (message.type === 'BATTLE_ENDED') {
        handleBattleEnd(message.data)
      }
    })
  }
}
```

### 6. 全局WebSocket状态管理

#### 创建WebSocket状态Store

```javascript
// stores/websocket.js
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { webSocketService } from '@/utils/websocket'

export const useWebSocketStore = defineStore('websocket', () => {
  const connected = ref(false)
  const connecting = ref(false)
  const reconnectAttempts = ref(0)

  const connect = async () => {
    try {
      connecting.value = true
      await webSocketService.connect()
      connected.value = true
      reconnectAttempts.value = 0
    } catch (error) {
      connected.value = false
      throw error
    } finally {
      connecting.value = false
    }
  }

  const disconnect = () => {
    webSocketService.disconnect()
    connected.value = false
    connecting.value = false
  }

  const getStatus = () => {
    return {
      connected: connected.value,
      connecting: connecting.value,
      reconnectAttempts: reconnectAttempts.value
    }
  }

  return {
    connected,
    connecting,
    reconnectAttempts,
    connect,
    disconnect,
    getStatus
  }
})
```

### 7. 错误处理和用户提示

#### 连接状态组件

```vue
<!-- components/WebSocketStatus.vue -->
<template>
  <el-alert
    v-if="!wsStore.connected"
    title="实时通信状态"
    :type="wsStore.connecting ? 'warning' : 'error'"
    :closable="false"
    show-icon
  >
    <template #default>
      <p v-if="wsStore.connecting">正在连接实时通信服务...</p>
      <p v-else>实时通信连接中断，正在尝试重连</p>
      <p>当前使用HTTP轮询模式，可能存在延迟</p>
    </template>
  </el-alert>
</template>

<script setup>
import { useWebSocketStore } from '@/stores/websocket'

const wsStore = useWebSocketStore()
</script>
```

### 8. 性能优化建议

#### 连接池管理

```javascript
// utils/websocketPool.js
class WebSocketPool {
  constructor() {
    this.connections = new Map()
    this.maxConnections = 3
  }

  getConnection(roomCode) {
    if (!this.connections.has(roomCode)) {
      if (this.connections.size >= this.maxConnections) {
        // 移除最旧的连接
        const oldestKey = this.connections.keys().next().value
        this.removeConnection(oldestKey)
      }

      this.connections.set(roomCode, new WebSocketService())
    }

    return this.connections.get(roomCode)
  }

  removeConnection(roomCode) {
    const connection = this.connections.get(roomCode)
    if (connection) {
      connection.disconnect()
      this.connections.delete(roomCode)
    }
  }
}

export const wsPool = new WebSocketPool()
```

#### 消息缓存和重放

```javascript
// utils/messageCache.js
class MessageCache {
  constructor(maxSize = 100) {
    this.cache = []
    this.maxSize = maxSize
  }

  addMessage(message) {
    this.cache.unshift({
      ...message,
      cachedAt: Date.now()
    })

    if (this.cache.length > this.maxSize) {
      this.cache = this.cache.slice(0, this.maxSize)
    }
  }

  getRecentMessages(roomCode, limit = 10) {
    return this.cache
      .filter(msg => msg.roomInfo?.roomCode === roomCode)
      .slice(0, limit)
  }

  clearOldMessages(maxAge = 300000) { // 5分钟
    const now = Date.now()
    this.cache = this.cache.filter(msg =>
      now - msg.cachedAt < maxAge
    )
  }
}

export const messageCache = new MessageCache()
```

## 部署注意事项

### 1. 服务器配置

确保后端服务器正确配置了WebSocket支持：

```yaml
# application.yml
server:
  port: 8080

spring:
  websocket:
    enabled: true
```

### 2. 反向代理配置

如果使用Nginx等反向代理，需要配置WebSocket支持：

```nginx
location /ws {
    proxy_pass http://backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_read_timeout 86400;
}
```

### 3. 防火墙配置

确保WebSocket端口（通常是HTTP端口）在防火墙中开放。

### 4. 负载均衡

如果使用多个后端实例，需要配置会话粘性（sticky sessions）或使用外部消息代理（如Redis）。

## 测试和调试

### 1. WebSocket连接测试

```javascript
// 测试WebSocket连接
const testWebSocket = async () => {
  try {
    await webSocketService.connect()
    console.log('✅ WebSocket连接测试成功')

    // 测试消息发送
    webSocketService.sendMessage('/app/test', { message: 'test' })

  } catch (error) {
    console.error('❌ WebSocket连接测试失败:', error)
  }
}
```

### 2. 消息流量监控

```javascript
// 添加消息统计
class WebSocketMonitor {
  constructor() {
    this.stats = {
      messagesSent: 0,
      messagesReceived: 0,
      connectionsCount: 0,
      lastActivity: null
    }
  }

  recordMessageSent() {
    this.stats.messagesSent++
    this.stats.lastActivity = Date.now()
  }

  recordMessageReceived() {
    this.stats.messagesReceived++
    this.stats.lastActivity = Date.now()
  }

  getStats() {
    return { ...this.stats }
  }
}

export const wsMonitor = new WebSocketMonitor()
```

通过以上完整的WebSocket集成方案，CodeDuel项目可以实现真正的实时通信，大大提升用户体验和系统响应性。
```
