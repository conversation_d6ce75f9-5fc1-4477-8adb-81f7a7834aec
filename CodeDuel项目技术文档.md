# CodeDuel项目技术文档

## 📖 项目概述

CodeDuel是一个基于微服务架构的在线编程对战平台，采用Spring Boot + Vue.js + Python的技术栈，支持实时匹配、房间对战、代码提交和评测等功能。

### 🏗️ 整体架构
```
前端(Vue.js) ←→ Java后端(Spring Boot) ←→ Python服务(Flask) ←→ Codeforces API
     ↓                    ↓
  WebSocket通信         MySQL数据库
```

## 🔧 技术栈总览

### 后端技术栈
- **Spring Boot 3.4.1** - 主框架
- **MyBatis-Plus 3.5.12** - ORM框架，数据库操作
- **MySQL** - 关系型数据库
- **WebSocket** - 实时通信
- **Lombok** - 简化Java代码
- **Swagger** - API文档生成

### 前端技术栈
- **Vue.js 3** - 前端框架，使用Composition API
- **Element Plus** - UI组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP请求库

### Python微服务
- **Flask** - 轻量级Web框架
- **requests** - HTTP客户端库
- **flask-cors** - 跨域支持

## 📁 项目结构详解

### 后端结构 (code-fuel-backend/)

#### 核心目录结构
```
src/main/java/com/xju/codeduel/
├── codeduelApplication.java     # 主启动类
├── web/controller/              # Web控制器层
├── controller/                  # 业务控制器层  
├── service/                     # 服务层接口
├── mapper/                      # 数据访问层
├── model/                       # 数据模型
├── config/                      # 配置类
├── common/                      # 公共工具类
├── event/                       # 事件处理
└── websocket/                   # WebSocket处理
```

#### 主要文件功能说明

**启动类**
- `codeduelApplication.java` - Spring Boot主启动类，配置MyBatis扫描和异步支持

**控制器层 (Controller)**
- `UsersController.java` - 用户相关API：登录、注册、用户信息管理
- `AdminController.java` - 管理员功能：题目数据更新、系统管理
- `AuthController.java` - 认证相关：获取当前用户信息
- `ProblemsTagsController.java` - 题目标签管理
- `MatchController.java` - 匹配对战功能
- `RoomController.java` - 房间对战功能

**服务层 (Service)**
- 业务逻辑处理层，实现具体的业务功能
- 包含接口定义和实现类

**数据访问层 (Mapper)**
- MyBatis映射器，处理数据库操作
- 对应XML映射文件在resources/mapper/目录下

**模型层 (Model)**
- `entity/` - 数据库实体类
- `dto/` - 数据传输对象
- `domain/` - 领域对象

**配置类 (Config)**
- Spring Boot配置类，如WebSocket配置、跨域配置等

**公共工具 (Common)**
- 通用工具类、响应封装、异常处理等

### Python微服务 (python-service/)

#### 文件说明
- `codeforces_api_service.py` - 主服务文件，提供Codeforces API代理服务
- `requirements.txt` - Python依赖配置
- `start_python_service.bat` - Windows启动脚本

#### 核心功能
1. **API代理服务** - 解决Java访问Codeforces API不稳定的问题
2. **数据获取** - 获取题目集、用户信息、比赛数据
3. **网络优化** - 重试机制、超时处理、错误恢复

### 前端结构 (code-fuel-frontend/)

#### 核心目录结构
```
src/
├── main.js                      # 应用入口文件
├── App.vue                      # 根组件
├── views/                       # 页面组件
│   ├── Login.vue               # 登录页面
│   ├── Dashboard.vue           # 主布局页面
│   ├── Admin.vue               # 管理员页面
│   └── dashboard/              # 主要功能页面
├── components/                  # 公共组件
├── router/                      # 路由配置
├── stores/                      # 状态管理
├── api/                         # API接口定义
├── utils/                       # 工具函数
└── assets/                      # 静态资源
```

#### 主要组件功能

**页面组件 (Views)**
- `Login.vue` - 用户登录界面，支持Codeforces账号登录
- `Dashboard.vue` - 主布局，包含导航和路由出口
- `Admin.vue` - 管理员界面，数据管理功能

**功能组件 (Components)**
- `BattleRoom.vue` - 对战房间界面，实时对战功能
- `RoomManager.vue` - 房间管理，创建/加入房间
- `RoomWaiting.vue` - 房间等待界面，等待对手加入

**工具模块**
- `api/api.js` - 所有后端API接口定义
- `utils/request.js` - Axios配置和请求拦截
- `utils/websocket.js` - WebSocket通信封装
- `stores/userInfo.js` - 用户状态管理

## 🎯 核心功能实现

### 1. 用户系统
- **Codeforces集成** - 支持CF账号登录验证
- **Rating系统** - 基于ELO算法的技能评级
- **用户管理** - 注册、登录、信息维护

### 2. 对战系统
- **匹配对战** - 自动匹配相近水平对手
- **房间对战** - 创建/加入房间进行友谊赛
- **实时通信** - WebSocket实现状态同步

### 3. 题目系统
- **数据同步** - 从Codeforces获取最新题目
- **标签管理** - 题目分类和筛选
- **难度分级** - 根据rating分级

### 4. 评测系统
- **多语言支持** - Java、Python、C++等
- **安全执行** - Docker容器化代码执行
- **实时反馈** - 编译错误、运行结果

## 🔄 数据流向

### 用户登录流程
1. 前端发送登录请求 → Java后端
2. 后端调用Python服务 → 验证CF账号
3. Python服务请求 → Codeforces API
4. 验证成功后创建会话 → 返回用户信息

### 题目数据更新流程
1. 管理员触发更新 → Java后端
2. 后端调用Python服务 → 获取题目数据
3. Python服务批量请求 → Codeforces API
4. 数据处理后存储 → MySQL数据库

### 实时对战流程
1. 用户操作 → 前端WebSocket
2. 消息传递 → Java后端WebSocket处理器
3. 业务逻辑处理 → 广播给房间内用户
4. 状态同步 → 所有客户端实时更新

## 🚀 部署和运行

### 环境要求
- Java 17+
- Node.js 16+
- Python 3.8+
- MySQL 8.0+

### 启动顺序
1. **启动MySQL数据库**
2. **启动Java后端** (端口8080)
   ```bash
   cd code-fuel-backend
   mvn spring-boot:run
   ```
3. **启动Python服务** (端口5000)
   ```bash
   cd code-fuel-backend/python-service
   python codeforces_api_service.py
   ```
4. **启动前端应用** (端口5173)
   ```bash
   cd code-fuel-frontend
   npm install && npm run dev
   ```

## 📊 技术特色

### 微服务架构
- **服务分离** - Java主业务 + Python API代理
- **独立部署** - 各服务可独立扩展
- **技术选型** - 各服务使用最适合的技术栈

### 实时通信
- **WebSocket** - 双向实时通信
- **状态同步** - 房间状态实时更新
- **消息广播** - 多用户同步

### 数据管理
- **MyBatis-Plus** - 简化数据库操作
- **代码生成** - 自动生成CRUD代码
- **事务管理** - Spring事务支持

### 前端架构
- **组件化** - Vue3 Composition API
- **状态管理** - Pinia集中状态管理
- **路由守卫** - 权限控制和页面保护

这个项目展示了现代Web应用的完整技术栈，从前端交互到后端业务逻辑，再到外部API集成，形成了一个完整的在线编程平台解决方案。

## 📋 详细文件功能清单

### Java后端核心文件详解

#### 控制器层文件
**UsersController.java** - 用户管理控制器
- 功能：用户注册、登录、信息查询、密码修改
- 知识点：Spring MVC、RESTful API、Session管理
- 主要接口：
  - `POST /api/users/login` - 用户登录
  - `POST /api/users/register` - 用户注册
  - `GET /api/users/id/{id}` - 根据ID查询用户
  - `PUT /api/users/update` - 更新用户信息

**AdminController.java** - 管理员控制器
- 功能：题目数据更新、标签管理、系统维护
- 知识点：异步处理、外部API调用、数据同步
- 主要接口：
  - `POST /api/admin/update-problems` - 更新题目数据
  - `POST /api/admin/update-tags` - 更新标签数据
  - `GET /api/admin/data-status` - 获取数据状态

**MatchController.java** - 匹配对战控制器
- 功能：自动匹配、ELO算法、对战记录
- 知识点：算法实现、并发处理、实时匹配
- 主要功能：匹配队列管理、分数计算、对战历史

**RoomController.java** - 房间对战控制器
- 功能：房间创建、加入、管理、对战流程
- 知识点：房间状态管理、WebSocket集成、并发控制
- 主要功能：房间生命周期管理、用户权限控制

#### 服务层架构
**Service接口层**
- `IUsersService` - 用户服务接口
- `IProblemsService` - 题目服务接口
- `ITagsService` - 标签服务接口
- `IMatchService` - 匹配服务接口
- `IRoomService` - 房间服务接口

**Service实现层 (impl/)**
- 业务逻辑具体实现
- 事务管理和数据处理
- 外部服务调用封装

#### 数据访问层
**Mapper接口**
- MyBatis映射器接口
- 数据库CRUD操作定义
- 复杂查询方法声明

**XML映射文件 (resources/mapper/)**
- SQL语句定义
- 结果映射配置
- 动态SQL实现

#### 模型层详解
**Entity实体类**
- `Users.java` - 用户实体，对应users表
- `Problems.java` - 题目实体，对应problems表
- `Tags.java` - 标签实体，对应tags表
- `Rooms.java` - 房间实体，对应rooms表
- `Matches.java` - 匹配记录实体

**DTO数据传输对象**
- 前后端数据交换格式
- 请求参数封装
- 响应数据结构

**Domain领域对象**
- 业务领域模型
- 复杂业务逻辑封装
- 业务规则实现

#### 配置类
**WebSocket配置**
- WebSocket端点注册
- 消息处理器配置
- 跨域设置

**数据库配置**
- MyBatis-Plus配置
- 数据源配置
- 事务管理配置

**跨域配置**
- CORS策略设置
- 允许的域名和方法
- 请求头配置

#### 公共工具类
**JsonResponse** - 统一响应格式
- 成功/失败响应封装
- 错误码定义
- 数据格式标准化

**SessionUtils** - 会话工具类
- 用户会话管理
- 登录状态检查
- 用户信息获取

**PythonServiceUtils** - Python服务调用工具
- HTTP请求封装
- 重试机制实现
- 异常处理

### Python微服务详解

**codeforces_api_service.py** - 核心服务文件
- 功能：Codeforces API代理服务
- 知识点：Flask框架、HTTP客户端、异常处理
- 主要功能：
  - 题目数据获取和缓存
  - 用户信息验证
  - 网络请求优化
  - 错误重试机制

**技术实现特点：**
- **网络优化**：重试机制、超时处理
- **数据处理**：JSON解析、数据清洗
- **服务稳定性**：异常捕获、日志记录
- **API设计**：RESTful接口、统一响应格式

### 前端Vue.js文件详解

#### 页面组件 (Views)
**Login.vue** - 登录页面
- 功能：用户登录界面、表单验证
- 知识点：Vue3 Composition API、Element Plus表单
- 技术特点：响应式验证、路由跳转、状态管理

**Dashboard.vue** - 主布局页面
- 功能：整体布局、导航菜单、路由出口
- 知识点：Vue Router、组件通信、布局设计
- 技术特点：响应式布局、权限控制、菜单管理

**Admin.vue** - 管理员页面
- 功能：数据管理、系统维护、操作监控
- 知识点：权限验证、异步操作、状态反馈
- 技术特点：实时状态更新、操作确认、错误处理

#### 功能组件 (Components)
**BattleRoom.vue** - 对战房间组件
- 功能：实时对战界面、代码编辑、提交评测
- 知识点：WebSocket通信、代码编辑器集成、实时同步
- 技术特点：
  - 实时状态同步
  - 代码编辑和高亮
  - 倒计时功能
  - 结果展示

**RoomManager.vue** - 房间管理组件
- 功能：创建房间、房间列表、参数设置
- 知识点：表单处理、数据验证、组件通信
- 技术特点：
  - 动态表单验证
  - 题目筛选功能
  - 房间状态管理

**RoomWaiting.vue** - 房间等待组件
- 功能：等待对手、房间信息展示、开始对战
- 知识点：轮询更新、WebSocket监听、状态管理
- 技术特点：
  - 实时参与者列表
  - 房主权限控制
  - 自动状态刷新

#### 工具模块
**api/api.js** - API接口定义
- 功能：所有后端接口的统一管理
- 知识点：Axios封装、请求拦截、响应处理
- 接口分类：
  - 用户相关接口
  - 房间管理接口
  - 题目数据接口
  - 管理员接口

**utils/request.js** - HTTP请求配置
- 功能：Axios实例配置、拦截器设置
- 知识点：请求/响应拦截、错误处理、Token管理
- 技术特点：
  - 自动Token添加
  - 统一错误处理
  - 请求/响应日志

**utils/websocket.js** - WebSocket通信封装
- 功能：WebSocket连接管理、消息处理
- 知识点：WebSocket API、事件处理、连接管理
- 技术特点：
  - 自动重连机制
  - 消息队列管理
  - 事件监听器

**stores/userInfo.js** - 用户状态管理
- 功能：用户信息存储、登录状态管理
- 知识点：Pinia状态管理、持久化存储
- 技术特点：
  - 响应式状态更新
  - 本地存储同步
  - 状态持久化

### 配置文件详解

#### 后端配置
**pom.xml** - Maven项目配置
- 依赖管理：Spring Boot、MyBatis-Plus、WebSocket等
- 插件配置：编译插件、Spring Boot插件
- 版本管理：统一依赖版本控制

**application.yml** - Spring Boot配置
- 数据库连接配置
- MyBatis配置
- 服务端口配置
- 日志配置

#### 前端配置
**package.json** - npm项目配置
- 依赖管理：Vue3、Element Plus、Vite等
- 脚本定义：开发、构建、预览命令
- 版本锁定：确保依赖版本一致性

**vite.config.js** - Vite构建配置
- 开发服务器配置
- 代理设置
- 插件配置
- 构建优化

### 数据库设计

**主要数据表：**
- `users` - 用户信息表
- `problems` - 题目信息表
- `tags` - 标签信息表
- `rooms` - 房间信息表
- `matches` - 匹配记录表
- `battle_records` - 对战记录表

**关系设计：**
- 用户与对战记录：一对多关系
- 题目与标签：多对多关系
- 房间与用户：多对多关系

## 🎓 涉及的技术知识点总结

### 后端技术知识点
1. **Spring Boot框架**：自动配置、依赖注入、AOP
2. **MyBatis-Plus**：ORM映射、代码生成、分页查询
3. **WebSocket**：实时通信、消息广播、连接管理
4. **RESTful API**：接口设计、HTTP方法、状态码
5. **数据库设计**：表结构设计、索引优化、事务管理
6. **微服务架构**：服务拆分、服务通信、负载均衡

### 前端技术知识点
1. **Vue.js 3**：Composition API、响应式系统、组件化
2. **Element Plus**：UI组件库、主题定制、表单验证
3. **Vue Router**：路由配置、导航守卫、动态路由
4. **Pinia**：状态管理、模块化、持久化
5. **Vite**：构建工具、热更新、模块打包
6. **WebSocket客户端**：连接管理、消息处理、事件监听

### Python技术知识点
1. **Flask框架**：路由定义、请求处理、响应格式化
2. **HTTP客户端**：requests库、异常处理、重试机制
3. **API设计**：RESTful接口、跨域处理、错误处理
4. **网络编程**：HTTP协议、JSON数据处理、超时控制

### 数据库技术知识点
1. **MySQL**：关系型数据库、SQL查询、索引优化
2. **数据库设计**：表结构设计、外键约束、数据完整性
3. **事务管理**：ACID特性、隔离级别、并发控制

### 系统架构知识点
1. **微服务架构**：服务拆分、API网关、服务发现
2. **前后端分离**：接口设计、跨域处理、状态管理
3. **实时通信**：WebSocket、长连接、消息推送
4. **缓存策略**：数据缓存、页面缓存、API缓存

这个项目是一个完整的现代Web应用实例，涵盖了从前端交互到后端业务逻辑，从数据存储到外部API集成的完整技术栈，是学习现代Web开发的优秀案例。

## 📂 完整文件清单与功能说明

### 后端Java文件详细清单

#### 控制器层 (Controller)
```
controller/
├── BattleRecordsController.java    # 对战记录管理
├── ChatMessagesController.java     # 聊天消息处理
├── CommentsController.java         # 评论功能
├── MatchController.java            # 匹配对战逻辑
├── PostsController.java            # 论坛帖子管理
├── ProblemsController.java         # 题目数据管理
├── RoomController.java             # 房间对战功能
├── TagsController.java             # 标签管理
└── UserRatingHistoriesController.java # 用户评分历史

web/controller/
├── AdminController.java            # 管理员功能
├── AuthController.java             # 认证相关
├── ProblemsTagsController.java     # 题目标签关联
└── UsersController.java            # 用户管理
```

#### 服务层 (Service)
```
service/
├── IBattleRecordsService.java      # 对战记录服务接口
├── IChatMessagesService.java       # 聊天服务接口
├── ICommentsService.java           # 评论服务接口
├── IMatchService.java              # 匹配服务接口
├── IPostsService.java              # 帖子服务接口
├── IProblemsService.java           # 题目服务接口
├── IRoomService.java               # 房间服务接口
├── ITagsService.java               # 标签服务接口
├── IUsersService.java              # 用户服务接口
├── IVerificationService.java       # 验证服务接口
├── FileService.java                # 文件服务
└── UserRoomService.java            # 用户房间关联服务

service/impl/                       # 服务实现类
├── BattleRecordsServiceImpl.java
├── ChatMessagesServiceImpl.java
├── CommentsServiceImpl.java
├── MatchServiceImpl.java
├── PostsServiceImpl.java
├── ProblemsServiceImpl.java
├── RoomServiceImpl.java
├── TagsServiceImpl.java
├── UsersServiceImpl.java
└── VerificationServiceImpl.java
```

#### 数据访问层 (Mapper)
```
mapper/
├── BattleRecordsMapper.java        # 对战记录数据访问
├── ChatMessagesMapper.java         # 聊天消息数据访问
├── CommentsMapper.java             # 评论数据访问
├── PostsMapper.java                # 帖子数据访问
├── ProblemsMapper.java             # 题目数据访问
├── ProblemsTagsMapper.java         # 题目标签关联
├── TagsMapper.java                 # 标签数据访问
├── UserBattleRecordMapper.java     # 用户对战记录
├── UserRatingHistoriesMapper.java  # 用户评分历史
└── UsersMapper.java                # 用户数据访问
```

#### 模型层 (Model)
```
model/
├── domain/                         # 领域对象
│   ├── BattleRecords.java         # 对战记录实体
│   ├── ChatMessages.java          # 聊天消息实体
│   ├── Comments.java              # 评论实体
│   ├── Posts.java                 # 帖子实体
│   ├── Problems.java              # 题目实体
│   ├── ProblemsTags.java          # 题目标签关联
│   ├── Tags.java                  # 标签实体
│   ├── UserBattleRecord.java      # 用户对战记录
│   ├── UserRatingHistories.java   # 用户评分历史
│   └── Users.java                 # 用户实体
├── dto/                           # 数据传输对象
│   ├── BattleResultDTO.java       # 对战结果DTO
│   ├── RoomInfoDTO.java           # 房间信息DTO
│   ├── UserInfoDTO.java           # 用户信息DTO
│   └── ProblemDTO.java            # 题目信息DTO
└── request/                       # 请求对象
    ├── LoginRequest.java          # 登录请求
    ├── RegisterRequest.java       # 注册请求
    ├── CreateRoomRequest.java     # 创建房间请求
    └── JoinRoomRequest.java       # 加入房间请求
```

#### 配置类 (Config)
```
config/
├── CorsConfig.java                # 跨域配置
├── DatabaseInitializer.java      # 数据库初始化
├── MatchScheduleConfig.java       # 匹配调度配置
└── WebSocketConfig.java           # WebSocket配置
```

#### 事件处理 (Event)
```
event/
├── BattleEndEvent.java            # 对战结束事件
└── BattleEndEventListener.java    # 对战结束事件监听器
```

#### WebSocket处理
```
websocket/
└── RoomWebSocketHandler.java      # 房间WebSocket处理器
```

#### 公共工具 (Common)
```
common/
├── JsonResponse.java              # 统一响应格式
├── config/                        # 公共配置
└── utls/                         # 工具类
    ├── SessionUtils.java          # 会话工具
    ├── PythonServiceUtils.java    # Python服务调用工具
    └── EloRatingUtils.java        # ELO评分算法工具
```

### 前端Vue.js文件详细清单

#### 页面组件 (Views)
```
views/
├── Login.vue                      # 登录页面
├── Dashboard.vue                  # 主布局页面
├── Admin.vue                      # 管理员页面
├── WebSocketTest.vue              # WebSocket测试页面
├── dashboard/                     # 主要功能页面
│   ├── Home.vue                   # 首页 - 数据概览和快速入口
│   ├── Battle.vue                 # 对战页面 - 匹配和房间对战
│   ├── Forum.vue                  # 论坛页面 - 帖子列表和讨论
│   ├── PostDetail.vue             # 帖子详情 - 帖子内容和评论
│   ├── Chat.vue                   # 聊天页面 - 实时聊天功能
│   ├── Ranking.vue                # 排行榜 - 用户排名和统计
│   └── Profile.vue                # 个人资料 - 用户信息和历史记录
├── admin/                         # 管理员页面
│   ├── DataManagement.vue         # 数据管理 - 题目和标签更新
│   ├── UserManagement.vue         # 用户管理 - 用户信息维护
│   └── SystemMonitor.vue          # 系统监控 - 服务状态监控
└── user/                          # 用户相关页面
    ├── Settings.vue               # 用户设置
    └── History.vue                # 历史记录
```

#### 功能组件 (Components)
```
components/
├── BattleRoom.vue                 # 对战房间 - 实时对战界面
├── RoomManager.vue                # 房间管理 - 创建和加入房间
├── RoomWaiting.vue                # 房间等待 - 等待对手加入
├── RoomWaitingWithWebSocket.vue   # WebSocket房间等待
├── VerificationDialog.vue         # 验证对话框 - 用户验证
└── CodeDuelLogo.vue               # 项目Logo组件
```

#### 工具模块 (Utils)
```
utils/
├── request.js                     # HTTP请求配置和拦截器
├── websocket.js                   # WebSocket通信封装
└── markdown.js                    # Markdown解析工具
```

#### API接口 (API)
```
api/
└── api.js                         # 所有后端API接口定义
```

#### 状态管理 (Stores)
```
stores/
└── userInfo.js                    # 用户信息状态管理
```

#### 路由配置 (Router)
```
router/
└── index.js                       # 路由定义和导航守卫
```

### Python微服务文件

```
python-service/
├── codeforces_api_service.py      # 主服务文件
├── requirements.txt               # Python依赖配置
└── start_python_service.bat       # Windows启动脚本
```

### 配置文件

#### 后端配置文件
```
src/main/resources/
├── application.yml                # Spring Boot主配置
├── mapper/                        # MyBatis XML映射文件
│   ├── BattleRecordsMapper.xml
│   ├── ChatMessagesMapper.xml
│   ├── CommentsMapper.xml
│   ├── PostsMapper.xml
│   ├── ProblemsMapper.xml
│   ├── TagsMapper.xml
│   └── UsersMapper.xml
└── static/                        # 静态资源文件
```

#### 前端配置文件
```
├── package.json                   # npm项目配置
├── vite.config.js                 # Vite构建配置
├── index.html                     # HTML入口文件
└── public/                        # 公共静态资源
```

### 数据库文件
```
├── db.sql                         # 数据库初始化脚本
```

### 项目文档
```
├── README.md                      # 项目说明文档
├── DEVELOPMENT.md                 # 开发指南
├── DEPLOYMENT.md                  # 部署指南
└── code-fuel-frontend/
    ├── 项目功能详细说明.md         # 前端功能详细说明
    ├── WebSocket实时通信详细说明.md # WebSocket通信机制
    └── WebSocket房间功能保证机制.md # 房间功能保证机制
```

## 🔍 核心功能模块分析

### 1. 用户认证模块
**涉及文件：**
- `UsersController.java` - 用户接口
- `AuthController.java` - 认证接口
- `IUsersService.java` - 用户服务
- `Login.vue` - 登录页面
- `userInfo.js` - 用户状态管理

**技术知识点：**
- Session管理
- 密码加密
- 用户验证
- 状态持久化

### 2. 实时对战模块
**涉及文件：**
- `RoomController.java` - 房间控制器
- `RoomWebSocketHandler.java` - WebSocket处理
- `BattleRoom.vue` - 对战界面
- `websocket.js` - WebSocket封装

**技术知识点：**
- WebSocket实时通信
- 房间状态管理
- 并发控制
- 消息广播

### 3. 题目数据模块
**涉及文件：**
- `AdminController.java` - 管理员接口
- `ProblemsController.java` - 题目接口
- `codeforces_api_service.py` - Python API服务
- `DataManagement.vue` - 数据管理页面

**技术知识点：**
- 外部API集成
- 数据同步
- 异步处理
- 微服务通信

### 4. 评分系统模块
**涉及文件：**
- `MatchController.java` - 匹配控制器
- `UserRatingHistoriesController.java` - 评分历史
- `EloRatingUtils.java` - ELO算法工具
- `Ranking.vue` - 排行榜页面

**技术知识点：**
- ELO评分算法
- 数据统计
- 排名计算
- 历史记录管理

这个项目展示了完整的现代Web应用开发流程，从需求分析到技术实现，从前端交互到后端业务逻辑，从数据存储到外部服务集成，是一个优秀的全栈开发学习案例。
