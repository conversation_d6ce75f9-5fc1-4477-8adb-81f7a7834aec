<template>
  <el-dialog
    :model-value="visible"
    title="房间等待"
    width="700px"
    :before-close="handleDialogClose"
    :close-on-click-modal="false"
  >
    <div class="room-waiting" v-loading="loading">
      <!-- WebSocket连接状态指示器 -->
      <el-alert
        v-if="!wsConnected"
        title="实时通信连接中断"
        type="warning"
        :closable="false"
        show-icon
        class="ws-status-alert"
      >
        <template #default>
          <p>WebSocket连接已断开，正在尝试重连...</p>
          <p>当前使用HTTP轮询模式，可能存在延迟</p>
        </template>
      </el-alert>

      <el-alert
        v-else
        title="实时通信已连接"
        type="success"
        :closable="false"
        show-icon
        class="ws-status-alert"
      >
        <template #default>
          <p>WebSocket连接正常，房间状态将实时同步</p>
        </template>
      </el-alert>

      <!-- 房间基本信息 -->
      <el-card class="room-info-card" shadow="never">
        <template #header>
          <div class="room-header">
            <div class="room-title">
              <h3>房间 {{ localRoomInfo.roomCode }}</h3>
              <el-tag :type="getRoomStatusType(localRoomInfo.status)" size="large">
                {{ formatRoomStatus(localRoomInfo.status) }}
              </el-tag>
            </div>
            <div class="room-actions">
              <el-button
                size="small"
                @click="copyRoomCode"
                :icon="DocumentCopy"
              >
                复制房间码
              </el-button>
              <el-button 
                size="small" 
                @click="refreshRoomInfo"
                :icon="Refresh"
                :loading="loading"
              >
                刷新
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="room-details">
          <div class="detail-item">
            <span class="label">房主：</span>
            <div class="creator-info">
              <el-avatar :src="localRoomInfo.creator?.avatar" :size="24" />
              <span>{{ localRoomInfo.creator?.codeforcesId }}</span>
            </div>
          </div>
          
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span>{{ formatTime(localRoomInfo.createTime) }}</span>
          </div>
          
          <div class="detail-item" v-if="localRoomInfo.description">
            <span class="label">房间描述：</span>
            <span>{{ localRoomInfo.description }}</span>
          </div>
          
          <div class="detail-item" v-if="localRoomInfo.problem">
            <span class="label">题目：</span>
            <span>{{ localRoomInfo.problem.title }}</span>
          </div>
        </div>
      </el-card>

      <!-- 参与者列表 -->
      <el-card class="participants-card" shadow="never">
        <template #header>
          <div class="participants-header">
            <h4>参与者 ({{ localRoomInfo.participantCount }}/{{ localRoomInfo.maxParticipants }})</h4>
          </div>
        </template>
        
        <div class="participants-list">
          <div 
            v-for="participant in localRoomInfo.participants" 
            :key="participant.id"
            class="participant-item"
            :class="{ 'is-creator': participant.id === localRoomInfo.creator?.id }"
          >
            <el-avatar :src="participant.avatar" :size="40" />
            <div class="participant-info">
              <div class="participant-name">
                {{ participant.codeforcesId }}
                <el-tag v-if="participant.id === localRoomInfo.creator?.id" size="small" type="warning">
                  房主
                </el-tag>
              </div>
              <div class="participant-rating">
                <el-icon><Trophy /></el-icon>
                <span>{{ participant.rating || 'Unrated' }}</span>
              </div>
            </div>
          </div>
          
          <!-- 空位占位符 -->
          <div 
            v-for="i in (localRoomInfo.maxParticipants - localRoomInfo.participantCount)" 
            :key="'empty-' + i"
            class="participant-item empty-slot"
          >
            <el-avatar :size="40">
              <el-icon><Avatar /></el-icon>
            </el-avatar>
            <div class="participant-info">
              <div class="participant-name">等待玩家加入...</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 等待提示 -->
      <div class="waiting-tips" v-if="localRoomInfo.status === 'WAITING'">
        <el-alert
          title="等待其他玩家加入"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>请将房间码 <strong>{{ localRoomInfo.roomCode }}</strong> 分享给其他玩家</p>
            <p>当房间人数达到 {{ localRoomInfo.maxParticipants }} 人时即可开始对战</p>
          </template>
        </el-alert>
      </div>

      <!-- 准备就绪提示 -->
      <div class="ready-tips" v-if="localRoomInfo.status === 'READY'">
        <el-alert
          title="房间已准备就绪"
          type="success"
          :closable="false"
          show-icon
        >
          <template #default>
            <p v-if="isCreator">所有玩家已加入，您可以开始对战了！</p>
            <p v-else>等待房主开始对战...</p>
          </template>
        </el-alert>
      </div>

      <!-- 实时消息显示 -->
      <div class="room-messages" v-if="recentMessages.length > 0">
        <el-card class="messages-card" shadow="never">
          <template #header>
            <h4>房间动态</h4>
          </template>
          <div class="messages-list">
            <div 
              v-for="msg in recentMessages" 
              :key="msg.id"
              class="message-item"
              :class="msg.type"
            >
              <el-icon class="message-icon">
                <InfoFilled v-if="msg.type === 'info'" />
                <SuccessFilled v-else-if="msg.type === 'success'" />
                <WarningFilled v-else-if="msg.type === 'warning'" />
              </el-icon>
              <span class="message-text">{{ msg.text }}</span>
              <span class="message-time">{{ formatMessageTime(msg.timestamp) }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleLeaveRoom" :icon="Close">
          离开房间
        </el-button>
        <el-button
          v-if="canStartBattle"
          type="primary"
          @click="handleStartBattle"
          :loading="startingBattle"
          :icon="VideoPlay"
        >
          开始对战
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Avatar,
  Trophy,
  Clock,
  DocumentCopy,
  Close,
  VideoPlay,
  Refresh,
  InfoFilled,
  SuccessFilled,
  WarningFilled
} from '@element-plus/icons-vue'
import { getRoomInfo, startBattle, leaveCurrentRoom } from '@/api/api'
import { useUserInfoStore } from '@/stores/userInfo'
import { webSocketService } from '@/utils/websocket'

// ==================== 组件属性和事件 ====================

const props = defineProps({
  roomInfo: {
    type: Object,
    required: false,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'battle-start', 'room-update'])

// ==================== 响应式数据 ====================

const userInfoStore = useUserInfoStore()

// 房间信息（本地副本，用于实时更新）
const localRoomInfo = ref(props.roomInfo ? { ...props.roomInfo } : null)

// 加载状态
const loading = ref(false)
const startingBattle = ref(false)

// WebSocket连接状态
const wsConnected = ref(false)

// 实时消息列表
const recentMessages = ref([])

// 定时器
let refreshTimer = null

// 当前用户信息
const currentUser = computed(() => userInfoStore.userInfo)

// 是否为房主
const isCreator = computed(() => {
  return currentUser.value && localRoomInfo.value.creator && 
         currentUser.value.id === localRoomInfo.value.creator.id
})

// 是否可以开始对战
const canStartBattle = computed(() => {
  return localRoomInfo.value.status === 'READY' && 
         localRoomInfo.value.participantCount >= 2 &&
         isCreator.value
})

// ==================== WebSocket相关方法 ====================

/**
 * 连接WebSocket并订阅房间消息
 */
const connectWebSocket = async () => {
  try {
    // 如果WebSocket未连接，先连接
    if (!webSocketService.isConnected()) {
      await webSocketService.connect()
    }
    
    wsConnected.value = true
    
    // 订阅房间消息
    webSocketService.subscribeToRoom(localRoomInfo.value.roomCode, handleWebSocketMessage)
    
    // 发送加入房间消息
    webSocketService.sendJoinRoom(localRoomInfo.value.roomCode, currentUser.value)
    
    console.log('✅ WebSocket连接成功，已订阅房间消息')
    
    // 停止HTTP轮询
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    
  } catch (error) {
    console.error('❌ WebSocket连接失败:', error)
    wsConnected.value = false
    
    // 启动HTTP轮询作为备用方案
    startHttpPolling()
  }
}

/**
 * 处理WebSocket消息
 */
const handleWebSocketMessage = (message) => {
  console.log('📨 收到WebSocket消息:', message)
  
  // 添加到消息列表
  addRecentMessage(message.message, getMessageType(message.type), message.timestamp)
  
  switch (message.type) {
    case 'USER_JOINED':
      ElMessage.success(message.message)
      localRoomInfo.value = message.roomInfo
      emit('room-update', message.roomInfo)
      break
      
    case 'USER_LEFT':
      ElMessage.info(message.message)
      localRoomInfo.value = message.roomInfo
      emit('room-update', message.roomInfo)
      break
      
    case 'BATTLE_STARTED':
      ElMessage.success(message.message)
      localRoomInfo.value = message.roomInfo
      emit('battle-start', message.roomInfo)
      break
      
    case 'ROOM_STATUS_CHANGED':
      localRoomInfo.value = message.roomInfo
      emit('room-update', message.roomInfo)
      break
      
    default:
      console.log('未知消息类型:', message.type)
  }
}

/**
 * 断开WebSocket连接
 */
const disconnectWebSocket = () => {
  if (localRoomInfo.value?.roomCode) {
    // 发送离开房间消息
    webSocketService.sendLeaveRoom(localRoomInfo.value.roomCode, currentUser.value)
    
    // 取消订阅
    webSocketService.unsubscribeFromRoom(localRoomInfo.value.roomCode)
  }
  
  wsConnected.value = false
}

/**
 * 启动HTTP轮询（WebSocket备用方案）
 */
const startHttpPolling = () => {
  if (refreshTimer) return
  
  console.log('🔄 启动HTTP轮询模式')
  refreshTimer = setInterval(refreshRoomInfo, 5000) // 5秒轮询一次
}

// ==================== 其他方法 ====================

/**
 * 刷新房间信息
 */
const refreshRoomInfo = async () => {
  if (!localRoomInfo.value.roomCode) return
  
  try {
    const response = await getRoomInfo(localRoomInfo.value.roomCode)
    
    if (response.status && response.data) {
      const oldStatus = localRoomInfo.value.status
      localRoomInfo.value = response.data
      
      // 如果状态发生变化，通知父组件
      if (oldStatus !== response.data.status) {
        emit('room-update', response.data)
        
        // 如果对战开始，自动跳转
        if (response.data.status === 'BATTLING') {
          emit('battle-start', response.data)
        }
      }
      
    } else {
      if (localRoomInfo.value.status !== 'BATTLING') {
        ElMessage.error('房间可能已关闭')
        emit('close')
      }
    }
    
  } catch (error) {
    console.error('❌ 刷新房间信息失败:', error)
  }
}

/**
 * 开始对战
 */
const handleStartBattle = async () => {
  if (!canStartBattle.value) {
    ElMessage.warning('当前无法开始对战')
    return
  }
  
  startingBattle.value = true
  
  try {
    // 优先通过WebSocket发送消息
    if (wsConnected.value) {
      webSocketService.sendStartBattle(localRoomInfo.value.roomCode, currentUser.value)
    }
    
    // 同时调用HTTP API确保操作成功
    const response = await startBattle(localRoomInfo.value.roomCode)
    
    if (response.status && response.data) {
      ElMessage.success('对战开始！')
      localRoomInfo.value = response.data
      emit('battle-start', response.data)
    } else {
      ElMessage.error(response.message || '开始对战失败')
    }
    
  } catch (error) {
    console.error('❌ 开始对战失败:', error)
    ElMessage.error('开始对战失败: ' + error.message)
  } finally {
    startingBattle.value = false
  }
}

/**
 * 添加最近消息
 */
const addRecentMessage = (text, type, timestamp) => {
  const message = {
    id: Date.now() + Math.random(),
    text,
    type,
    timestamp: timestamp || Date.now()
  }
  
  recentMessages.value.unshift(message)
  
  // 只保留最近10条消息
  if (recentMessages.value.length > 10) {
    recentMessages.value = recentMessages.value.slice(0, 10)
  }
}

/**
 * 获取消息类型
 */
const getMessageType = (wsMessageType) => {
  switch (wsMessageType) {
    case 'USER_JOINED':
    case 'BATTLE_STARTED':
      return 'success'
    case 'USER_LEFT':
      return 'warning'
    default:
      return 'info'
  }
}

/**
 * 格式化消息时间
 */
const formatMessageTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
}

// ==================== 其他现有方法保持不变 ====================
// 这里省略了其他方法的实现，如：
// - copyRoomCode
// - handleDialogClose
// - handleLeaveRoom
// - formatRoomStatus
// - getRoomStatusType
// - formatTime

// ==================== 生命周期钩子 ====================

onMounted(() => {
  // 连接WebSocket
  connectWebSocket()
  
  // 立即刷新一次房间信息
  refreshRoomInfo()
})

onUnmounted(() => {
  // 断开WebSocket连接
  disconnectWebSocket()
  
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})

// 监听props变化
watch(() => props.roomInfo, (newRoomInfo) => {
  if (newRoomInfo) {
    localRoomInfo.value = { ...newRoomInfo }
  }
}, { deep: true })

// 监听visible变化
watch(() => props.visible, (visible) => {
  if (visible) {
    connectWebSocket()
    refreshRoomInfo()
  } else {
    disconnectWebSocket()
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
})
</script>

<style scoped lang="scss">
// 样式保持与原组件相同，添加WebSocket状态相关样式

.ws-status-alert {
  margin-bottom: 20px;
}

.room-messages {
  margin-top: 20px;
  
  .messages-card {
    .messages-list {
      max-height: 200px;
      overflow-y: auto;
      
      .message-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .message-icon {
          margin-right: 8px;
          
          &.success {
            color: #67c23a;
          }
          
          &.warning {
            color: #e6a23c;
          }
          
          &.info {
            color: #409eff;
          }
        }
        
        .message-text {
          flex: 1;
          font-size: 14px;
        }
        
        .message-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

// 其他样式保持不变...
</style>
