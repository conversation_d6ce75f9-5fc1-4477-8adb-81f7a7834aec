<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Download,
  Edit,
  View,
  Warning
} from '@element-plus/icons-vue'
import { getProblemsPage, updateProblemsFromCodeforces } from '@/api/api'

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref({
  table: false,
  update: false
})

// 题目列表数据
const problemData = reactive({
  list: [],
  total: 0,
  current: 1,
  size: 10
})

// 搜索表单
const searchForm = reactive({
  title: '',
  minDifficulty: null,
  maxDifficulty: null
})

// 更新配置
const updateConfig = reactive({
  maxPages: 10,
  minRating: 800,
  maxRating: 3500
})

// ==================== 方法定义 ====================

/**
 * 加载题目列表数据
 * 
 * 功能说明：
 * 1. 根据当前分页和搜索条件查询题目
 * 2. 支持标题模糊搜索和难度范围筛选
 * 3. 更新表格数据和分页信息
 */
const loadProblems = async () => {
  loading.value.table = true
  
  try {
    console.log('🔍 加载题目列表，参数:', {
      pageNo: problemData.current,
      pageSize: problemData.size,
      ...searchForm
    })
    
    const params = {
      pageNo: problemData.current,
      pageSize: problemData.size,
      title: searchForm.title || undefined,
      minDifficulty: searchForm.minDifficulty || undefined,
      maxDifficulty: searchForm.maxDifficulty || undefined
    }
    
    const response = await getProblemsPage(params)
    
    if (response.status) {
      const data = response.data
      problemData.list = data.records || []
      problemData.total = data.total || 0
      problemData.current = data.current || 1
      
      console.log('✅ 题目列表加载成功，共', problemData.total, '条记录')
    } else {
      ElMessage.error(response.message || '加载题目列表失败')
    }
    
  } catch (error) {
    console.error('❌ 加载题目列表失败:', error)
    ElMessage.error('加载题目列表失败: ' + error.message)
  } finally {
    loading.value.table = false
  }
}

/**
 * 搜索题目
 * 
 * 功能说明：
 * 1. 重置到第一页
 * 2. 根据搜索条件重新加载数据
 */
const handleSearch = () => {
  console.log('🔍 执行题目搜索，条件:', searchForm)
  problemData.current = 1
  loadProblems()
}

/**
 * 重置搜索条件
 * 
 * 功能说明：
 * 1. 清空所有搜索条件
 * 2. 重新加载数据
 */
const handleReset = () => {
  console.log('🔄 重置搜索条件')
  searchForm.title = ''
  searchForm.minDifficulty = null
  searchForm.maxDifficulty = null
  problemData.current = 1
  loadProblems()
}

/**
 * 分页变化处理
 * 
 * 功能说明：
 * 1. 更新当前页码
 * 2. 重新加载数据
 */
const handlePageChange = (page) => {
  console.log('📄 切换到第', page, '页')
  problemData.current = page
  loadProblems()
}

/**
 * 每页大小变化处理
 * 
 * 功能说明：
 * 1. 更新每页大小
 * 2. 重置到第一页
 * 3. 重新加载数据
 */
const handleSizeChange = (size) => {
  console.log('📏 每页大小变更为', size)
  problemData.size = size
  problemData.current = 1
  loadProblems()
}

/**
 * 更新题目数据
 * 
 * 功能说明：
 * 1. 调用Python服务从Codeforces更新题目数据
 * 2. 更新完成后刷新列表
 * 3. 同时更新题目标签关系
 */
const handleUpdateProblems = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要从Codeforces更新题目数据吗？这个过程可能需要几分钟时间。',
      '确认更新',
      {
        confirmButtonText: '确定更新',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '更新中...'
          }
          done()
        }
      }
    )
    
    loading.value.update = true
    
    console.log('🔄 开始更新题目数据，配置:', updateConfig)
    
    const response = await updateProblemsFromCodeforces(updateConfig)
    
    if (response.status) {
      ElMessage.success('题目数据更新成功！')
      console.log('✅ 题目数据更新完成:', response.data)
      
      // 刷新列表
      await loadProblems()
    } else {
      ElMessage.error(response.message || '更新题目数据失败')
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 更新题目数据失败:', error)
      ElMessage.error('更新题目数据失败: ' + error.message)
    }
  } finally {
    loading.value.update = false
  }
}

/**
 * 格式化难度显示
 * 
 * 功能说明：
 * 1. 根据难度值返回对应的颜色标签
 * 2. 提供直观的难度等级显示
 */
const getDifficultyType = (difficulty) => {
  if (difficulty <= 1200) return 'success'
  if (difficulty <= 1600) return 'primary'
  if (difficulty <= 2000) return 'warning'
  return 'danger'
}

/**
 * 格式化时间显示
 * 
 * 功能说明：
 * 1. 将时间戳转换为可读格式
 * 2. 处理空值情况
 */
const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

// ==================== 生命周期 ====================

onMounted(() => {
  console.log('🚀 题目管理页面初始化')
  loadProblems()
})
</script>

<template>
  <div class="problem-management">
    <!-- 页面标题 -->
    <el-card class="header-card" shadow="never">
      <template #header>
        <div class="header">
          <h2>题目管理</h2>
          <el-button 
            type="primary"
            @click="handleUpdateProblems"
            :loading="loading.update"
            :icon="Download"
            size="large"
          >
            {{ loading.update ? '更新中...' : '更新题目数据' }}
          </el-button>
        </div>
      </template>
      
      <el-alert
        title="题目管理说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>管理系统中的题目数据，支持查看、搜索和更新功能。</p>
          <p><strong>更新功能：</strong>点击"更新题目数据"按钮可以从Codeforces获取最新题目数据，同时更新题目标签关系。</p>
        </template>
      </el-alert>
    </el-card>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="题目标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入题目标题"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="最小难度">
          <el-input-number
            v-model="searchForm.minDifficulty"
            placeholder="最小难度"
            :min="800"
            :max="3500"
            :step="100"
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item label="最大难度">
          <el-input-number
            v-model="searchForm.maxDifficulty"
            placeholder="最大难度"
            :min="800"
            :max="3500"
            :step="100"
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="handleReset" :icon="Refresh">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 题目列表 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>题目列表</span>
          <el-text type="info">共 {{ problemData.total }} 条记录</el-text>
        </div>
      </template>
      
      <el-table
        :data="problemData.list"
        :loading="loading.table"
        stripe
        style="width: 100%"
        empty-text="暂无题目数据"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="title" label="题目标题" min-width="200">
          <template #default="{ row }">
            <el-text class="problem-title">{{ row.title }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column prop="difficulty" label="难度" width="120">
          <template #default="{ row }">
            <el-tag :type="getDifficultyType(row.difficulty)" size="small">
              {{ row.difficulty }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="contestId" label="比赛ID" width="100" />
        
        <el-table-column prop="problemId" label="题目ID" width="100" />
        
        <el-table-column prop="createdTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createdTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.updateTime) }}
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="problemData.current"
          v-model:page-size="problemData.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="problemData.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.problem-management {
  padding: 20px;

  .header-card {
    margin-bottom: 20px;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }

  .search-card {
    margin-bottom: 20px;
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .problem-title {
      font-weight: 500;
      color: #409eff;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
