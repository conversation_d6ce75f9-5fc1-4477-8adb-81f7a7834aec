# CodeDuel 开发指南

## 🛠️ 开发环境搭建

### IDE 推荐
- **后端**: IntelliJ IDEA / Eclipse
- **前端**: VS Code / WebStorm
- **数据库**: MySQL Workbench / Navicat

### 开发工具
- **版本控制**: Git
- **API 测试**: Postman / Apifox
- **数据库管理**: MySQL Workbench
- **代码格式化**: Prettier (前端) / Google Java Format (后端)

## 📁 项目结构详解

### 后端结构 (Spring Boot)

```
code-fuel-backend/
├── src/main/java/com/xju/codeduel/
│   ├── web/controller/          # 控制器层
│   │   ├── MatchController.java # 匹配相关接口
│   │   ├── RoomController.java  # 房间相关接口
│   │   └── UsersController.java # 用户相关接口
│   ├── service/                 # 服务层接口
│   │   └── impl/               # 服务层实现
│   ├── mapper/                 # 数据访问层
│   ├── model/                  # 数据模型
│   │   ├── entity/            # 实体类
│   │   ├── dto/               # 数据传输对象
│   │   └── domain/            # 领域对象
│   ├── config/                # 配置类
│   ├── event/                 # 事件处理
│   └── common/                # 公共工具类
├── src/main/resources/
│   ├── application.yml        # 主配置文件
│   ├── mapper/               # MyBatis XML 映射文件
│   └── static/               # 静态资源
└── python-service/           # Python 微服务
```

### 前端结构 (Vue 3)

```
code-fuel-frontend/
├── src/
│   ├── views/                # 页面组件
│   │   ├── dashboard/       # 主要功能页面
│   │   └── auth/           # 认证相关页面
│   ├── components/         # 公共组件
│   │   ├── BattleRoom.vue  # 对战房间组件
│   │   └── RoomManager.vue # 房间管理组件
│   ├── api/               # API 接口定义
│   ├── stores/            # 状态管理 (Pinia)
│   ├── router/            # 路由配置
│   ├── utils/             # 工具函数
│   └── assets/            # 静态资源
├── public/                # 公共静态文件
└── package.json          # 项目依赖配置
```

## 🔧 开发规范

### 代码规范

#### Java 代码规范
```java
/**
 * 类注释：简要描述类的功能和用途
 * 
 * <AUTHOR>
 * @version 版本号
 * @since 创建日期
 */
@Service
public class ExampleService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExampleService.class);
    
    /**
     * 方法注释：描述方法功能、参数和返回值
     * 
     * @param param 参数描述
     * @return 返回值描述
     */
    public String exampleMethod(String param) {
        // 方法实现
        return "result";
    }
}
```

#### JavaScript/Vue 代码规范
```javascript
/**
 * 组件注释：描述组件功能和用途
 * 
 * <AUTHOR>
 * @version 版本号
 * @since 创建日期
 */

// 使用 Composition API
const { ref, computed, onMounted } = Vue

// 响应式数据
const data = ref(null)

// 计算属性
const computedValue = computed(() => {
  return data.value ? data.value.length : 0
})

// 方法定义
const handleClick = () => {
  // 方法实现
}
```

### 命名规范

#### 后端命名
- **类名**: PascalCase (如 `UserService`)
- **方法名**: camelCase (如 `getUserInfo`)
- **常量**: UPPER_SNAKE_CASE (如 `MAX_RETRY_COUNT`)
- **包名**: 小写 + 点分隔 (如 `com.xju.codeduel.service`)

#### 前端命名
- **组件名**: PascalCase (如 `BattleRoom.vue`)
- **变量/方法**: camelCase (如 `userName`, `handleSubmit`)
- **常量**: UPPER_SNAKE_CASE (如 `API_BASE_URL`)
- **CSS 类名**: kebab-case (如 `.battle-room`)

### Git 提交规范

```bash
# 提交格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      修复 bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(match): 添加智能匹配算法
fix(room): 修复房间状态同步问题
docs(api): 更新 API 文档
```

## 🧪 测试指南

### 后端测试

#### 单元测试
```java
@SpringBootTest
class UserServiceTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    void testGetUserInfo() {
        // 测试用户信息获取
        User user = userService.getUserById(1L);
        assertNotNull(user);
        assertEquals("testUser", user.getCodeforcesId());
    }
}
```

#### API 测试
```bash
# 使用 curl 测试 API
curl -X POST http://localhost:8080/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"codeforcesId":"test","password":"123456"}'
```

### 前端测试

#### 组件测试
```javascript
import { mount } from '@vue/test-utils'
import BattleRoom from '@/components/BattleRoom.vue'

describe('BattleRoom', () => {
  it('renders properly', () => {
    const wrapper = mount(BattleRoom, {
      props: { roomInfo: mockRoomInfo }
    })
    expect(wrapper.text()).toContain('对战房间')
  })
})
```

## 🔄 开发流程

### 功能开发流程

1. **需求分析**
   - 理解功能需求
   - 设计技术方案
   - 评估开发工作量

2. **数据库设计**
   - 设计表结构
   - 创建索引
   - 编写 SQL 脚本

3. **后端开发**
   - 创建实体类和 DTO
   - 实现 Mapper 接口
   - 编写 Service 业务逻辑
   - 实现 Controller 接口

4. **前端开发**
   - 设计页面布局
   - 实现组件功能
   - 集成 API 接口
   - 添加交互逻辑

5. **测试验证**
   - 单元测试
   - 集成测试
   - 功能测试
   - 性能测试

6. **代码审查**
   - 代码规范检查
   - 逻辑审查
   - 安全性检查
   - 性能优化建议

### 调试技巧

#### 后端调试
```java
// 使用日志调试
logger.debug("用户ID: {}, 操作: {}", userId, operation);
logger.info("匹配成功: {} vs {}", user1.getName(), user2.getName());
logger.error("匹配失败: {}", e.getMessage(), e);

// 使用断点调试
// 在 IDE 中设置断点，逐步调试代码执行流程
```

#### 前端调试
```javascript
// 使用 console 调试
console.log('用户信息:', userInfo)
console.error('API 请求失败:', error)

// 使用 Vue DevTools
// 在浏览器中安装 Vue DevTools 扩展

// 使用断点调试
debugger; // 在需要调试的地方添加断点
```

## 📚 学习资源

### 技术文档
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [Vue.js 官方文档](https://vuejs.org/)
- [Element Plus 组件库](https://element-plus.org/)
- [MyBatis-Plus 文档](https://baomidou.com/)

### 最佳实践
- RESTful API 设计规范
- 前后端分离架构
- 微服务设计模式
- 数据库设计原则

## 🤝 贡献指南

1. Fork 项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建 Pull Request
5. 代码审查和合并

欢迎提交 Issue 和 Pull Request！
