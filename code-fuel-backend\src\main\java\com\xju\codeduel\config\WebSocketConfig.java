package com.xju.codeduel.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * WebSocket配置类
 * 
 * 功能说明：
 * 1. 配置WebSocket消息代理，支持实时通信
 * 2. 设置STOMP端点，提供WebSocket连接入口
 * 3. 配置消息路由和订阅机制
 * 
 * 技术实现：
 * - 使用Spring WebSocket + STOMP协议
 * - 内存消息代理：适用于单机部署，性能高
 * - SockJS支持：兼容不支持WebSocket的浏览器
 * 
 * 消息路由设计：
 * - /app/** : 客户端发送消息的前缀
 * - /topic/** : 服务端广播消息的前缀（一对多）
 * - /queue/** : 服务端点对点消息的前缀（一对一）
 * 
 * 房间通信场景：
 * - /topic/room/{roomCode} : 房间内广播消息
 * - /queue/user/{userId} : 用户私人消息
 * - /app/room/join : 客户端加入房间
 * - /app/room/leave : 客户端离开房间
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    /**
     * 配置消息代理
     * 
     * 功能说明：
     * 1. 启用简单消息代理，处理订阅和广播
     * 2. 设置应用程序消息前缀，用于路由客户端消息
     * 3. 配置用户目标前缀，支持点对点消息
     * 
     * @param config 消息代理注册器
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // 启用简单消息代理，处理以"/topic"和"/queue"开头的消息
        // /topic用于广播消息（一对多），/queue用于点对点消息（一对一）
        config.enableSimpleBroker("/topic", "/queue");
        
        // 设置应用程序消息前缀，客户端发送消息时需要以"/app"开头
        // 例如：客户端发送到"/app/room/join"，会路由到@MessageMapping("/room/join")
        config.setApplicationDestinationPrefixes("/app");
        
        // 设置用户目标前缀，用于点对点消息
        // 例如：发送到"/user/123/queue/notification"会路由到用户ID为123的客户端
        config.setUserDestinationPrefix("/user");
    }

    /**
     * 注册STOMP端点
     * 
     * 功能说明：
     * 1. 设置WebSocket连接端点
     * 2. 启用SockJS支持，兼容不支持WebSocket的浏览器
     * 3. 配置跨域访问权限
     * 
     * @param registry STOMP端点注册器
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 注册WebSocket端点"/ws"，客户端通过此端点建立连接
        registry.addEndpoint("/ws")
                // 允许所有源的跨域访问（生产环境应该限制具体域名）
                .setAllowedOriginPatterns("*")
                // 启用SockJS支持，当WebSocket不可用时自动降级到其他传输方式
                // 如：长轮询、服务器发送事件等
                .withSockJS();
    }
}
