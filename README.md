# CodeDuel - 在线编程对战平台

## 📖 项目简介

CodeDuel 是一个基于 Spring Boot + Vue.js 的在线编程对战平台，支持实时匹配、房间对战、代码提交和评测等功能。

## 🏗️ 项目架构

```
CodeDuel/
├── code-fuel-backend/          # 后端服务 (Spring Boot)
│   ├── src/main/java/          # Java 源码
│   ├── src/main/resources/     # 配置文件
│   ├── python-service/         # Python 评测服务
│   └── pom.xml                 # Maven 配置
├── code-fuel-frontend/         # 前端应用 (Vue.js)
│   ├── src/                    # Vue 源码
│   ├── public/                 # 静态资源
│   └── package.json            # npm 配置
└── README.md                   # 项目说明
```

## ⚡ 快速开始

### 环境要求

- **Java**: JDK 17+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Python**: 3.8+ (用于代码评测)

### 启动步骤

1. **启动后端服务**
   ```bash
   cd code-fuel-backend
   mvn spring-boot:run
   ```
   后端服务将在 `http://localhost:8080` 启动

2. **启动前端应用**
   ```bash
   cd code-fuel-frontend
   npm install
   npm run dev
   ```
   前端应用将在 `http://localhost:5173` 启动

3. **启动 Python 评测服务**
   ```bash
   cd code-fuel-backend/python-service
   python app.py
   ```
   Python 服务将在 `http://localhost:5000` 启动

## 🎯 核心功能

### 🏆 对战系统
- **匹配对战**: 自动匹配相近水平的对手，使用 ELO 算法计算分数变化
- **房间对战**: 创建/加入房间进行友谊赛，分数不变
- **实时通信**: 基于 WebSocket 的实时对战状态同步

### 📊 评测系统
- **多语言支持**: Java, Python, C++ 等
- **安全沙箱**: Docker 容器化执行用户代码
- **实时反馈**: 编译错误、运行时错误、测试结果

### 👤 用户系统
- **Codeforces 集成**: 支持 Codeforces 账号登录
- **Rating 系统**: 基于 ELO 算法的技能评级
- **历史记录**: 完整的对战和 Rating 变化历史

## 🔧 技术栈

### 后端技术
- **Spring Boot 3.4.1**: 主框架
- **MyBatis-Plus**: ORM 框架
- **WebSocket**: 实时通信
- **MySQL**: 数据存储
- **Docker**: 代码执行沙箱

### 前端技术
- **Vue.js 3**: 前端框架
- **Element Plus**: UI 组件库
- **Vite**: 构建工具
- **SockJS**: WebSocket 客户端

### 评测服务
- **Python Flask**: 评测服务框架
- **Docker**: 安全代码执行
- **Requests**: HTTP 客户端

## 📚 详细文档

- [前端功能说明](./code-fuel-frontend/项目功能详细说明.md)
- [WebSocket 通信机制](./code-fuel-frontend/WebSocket实时通信详细说明.md)
- [房间功能保证机制](./code-fuel-frontend/WebSocket房间功能保证机制.md)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🎉 致谢

感谢所有为这个项目做出贡献的开发者！
