package com.xju.codeduel.common.utls;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * Python服务调用工具类
 * 
 * 功能说明：
 * 1. 调用Python服务获取用户提交记录
 * 2. 筛选两人都没做过的题目
 * 3. 验证用户提交状态
 * 
 * Python服务接口：
 * - GET /api/user/{handle}/submissions - 获取用户提交记录
 * - POST /api/filter-problems - 筛选题目
 * - GET /api/submission/{submissionId}/status - 获取提交状态
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Component
public class PythonServiceUtils {

    private static final Logger logger = LoggerFactory.getLogger(PythonServiceUtils.class);

    // Python服务的基础URL（默认运行在5000端口）
    private static final String PYTHON_SERVICE_BASE_URL = "http://localhost:5000";
    
    // HTTP客户端
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;

    public PythonServiceUtils() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 获取用户的提交记录
     *
     * 功能说明：
     * 1. 调用Python服务获取指定用户的所有提交记录
     * 2. 返回用户已经通过的题目ID列表
     *
     * @param codeforcesHandle 用户的Codeforces用户名
     * @return 用户已通过的题目ID列表
     */
    public List<String> getUserSolvedProblems(String codeforcesHandle) {
        logger.info("🔍 获取用户 {} 的提交记录", codeforcesHandle);

        try {
            // 构建请求URL
            String url = PYTHON_SERVICE_BASE_URL + "/api/codeforces/user/" + codeforcesHandle + "/submissions";

            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(30))
                    .GET()
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                // 解析响应
                JsonNode jsonNode = objectMapper.readTree(response.body());

                if (jsonNode.has("success") && jsonNode.get("success").asBoolean()) {
                    JsonNode solvedProblems = jsonNode.get("data").get("solvedProblems");

                    List<String> problemIds = new java.util.ArrayList<>();
                    if (solvedProblems.isArray()) {
                        for (JsonNode problemNode : solvedProblems) {
                            problemIds.add(problemNode.asText());
                        }
                    }

                    logger.info("✅ 用户 {} 已解决 {} 道题目", codeforcesHandle, problemIds.size());
                    return problemIds;
                } else {
                    String errorMsg = jsonNode.has("message") ? jsonNode.get("message").asText() : "未知错误";
                    logger.error("❌ Python服务返回错误: {}", errorMsg);
                    throw new RuntimeException("获取用户提交记录失败: " + errorMsg);
                }
            } else {
                logger.error("❌ Python服务请求失败，状态码: {}", response.statusCode());
                throw new RuntimeException("Python服务请求失败，状态码: " + response.statusCode());
            }

        } catch (IOException | InterruptedException e) {
            logger.error("❌ 调用Python服务失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用Python服务失败: " + e.getMessage());
        }
    }

    /**
     * 筛选两人都没做过的题目
     * 
     * 功能说明：
     * 1. 传入候选题目列表和两个用户的已解决题目列表
     * 2. 返回两人都没有做过的题目
     * 
     * @param candidateProblems 候选题目ID列表
     * @param user1SolvedProblems 用户1已解决的题目ID列表
     * @param user2SolvedProblems 用户2已解决的题目ID列表
     * @return 两人都没做过的题目ID列表
     */
    public List<String> filterUnsolved(List<String> candidateProblems, 
                                      List<String> user1SolvedProblems, 
                                      List<String> user2SolvedProblems) {
        
        logger.info("🔍 筛选两人都没做过的题目，候选题目: {}, 用户1已解决: {}, 用户2已解决: {}", 
                   candidateProblems.size(), user1SolvedProblems.size(), user2SolvedProblems.size());
        
        try {
            // 构建请求数据
            Map<String, Object> requestData = Map.of(
                "candidateProblems", candidateProblems,
                "user1SolvedProblems", user1SolvedProblems,
                "user2SolvedProblems", user2SolvedProblems
            );
            
            String requestBody = objectMapper.writeValueAsString(requestData);
            
            // 构建请求URL
            String url = PYTHON_SERVICE_BASE_URL + "/api/codeforces/problems/filter";
            
            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(30))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();
            
            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            if (response.statusCode() == 200) {
                // 解析响应
                JsonNode jsonNode = objectMapper.readTree(response.body());
                
                if (jsonNode.has("success") && jsonNode.get("success").asBoolean()) {
                    JsonNode unsolvedProblems = jsonNode.get("data").get("unsolvedProblems");
                    
                    List<String> problemIds = new java.util.ArrayList<>();
                    if (unsolvedProblems.isArray()) {
                        for (JsonNode problemNode : unsolvedProblems) {
                            problemIds.add(problemNode.asText());
                        }
                    }
                    
                    logger.info("✅ 筛选出 {} 道两人都没做过的题目", problemIds.size());
                    return problemIds;
                } else {
                    String errorMsg = jsonNode.has("message") ? jsonNode.get("message").asText() : "未知错误";
                    logger.error("❌ Python服务返回错误: {}", errorMsg);
                    throw new RuntimeException("筛选题目失败: " + errorMsg);
                }
            } else {
                logger.error("❌ Python服务请求失败，状态码: {}", response.statusCode());
                throw new RuntimeException("Python服务请求失败，状态码: " + response.statusCode());
            }
            
        } catch (IOException | InterruptedException e) {
            logger.error("❌ 调用Python服务失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用Python服务失败: " + e.getMessage());
        }
    }

    /**
     * 验证用户提交状态
     * 
     * 功能说明：
     * 1. 根据用户名和题目ID检查最新提交状态
     * 2. 返回是否通过（Accepted）
     * 
     * @param codeforcesHandle 用户的Codeforces用户名
     * @param problemId 题目ID
     * @return 是否通过
     */
    public boolean checkSubmissionStatus(String codeforcesHandle, String problemId) {
        logger.info("🔍 检查用户 {} 对题目 {} 的提交状态", codeforcesHandle, problemId);
        
        try {
            // 构建请求URL
            String url = PYTHON_SERVICE_BASE_URL + "/api/codeforces/user/" + codeforcesHandle + "/problem/" + problemId + "/status";
            
            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(30))
                    .GET()
                    .build();
            
            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            if (response.statusCode() == 200) {
                // 解析响应
                JsonNode jsonNode = objectMapper.readTree(response.body());
                
                if (jsonNode.has("success") && jsonNode.get("success").asBoolean()) {
                    boolean isAccepted = jsonNode.get("data").get("isAccepted").asBoolean();
                    
                    logger.info("✅ 用户 {} 对题目 {} 的提交状态: {}", 
                               codeforcesHandle, problemId, isAccepted ? "通过" : "未通过");
                    return isAccepted;
                } else {
                    String errorMsg = jsonNode.has("message") ? jsonNode.get("message").asText() : "未知错误";
                    logger.error("❌ Python服务返回错误: {}", errorMsg);
                    return false;
                }
            } else {
                logger.error("❌ Python服务请求失败，状态码: {}", response.statusCode());
                return false;
            }
            
        } catch (IOException | InterruptedException e) {
            logger.error("❌ 调用Python服务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查Python服务是否可用
     * 
     * @return 是否可用
     */
    public boolean isServiceAvailable() {
        try {
            String url = PYTHON_SERVICE_BASE_URL + "/api/codeforces/health";
            
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(5))
                    .GET()
                    .build();
            
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            return response.statusCode() == 200;
            
        } catch (Exception e) {
            logger.warn("⚠️ Python服务不可用: {}", e.getMessage());
            return false;
        }
    }
}
