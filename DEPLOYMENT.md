# CodeDuel 部署指南

## 📋 环境要求

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 2GB 可用空间

### 软件依赖
- **Java**: JDK 17 或更高版本
- **Node.js**: 16.0 或更高版本
- **MySQL**: 8.0 或更高版本
- **Python**: 3.8 或更高版本
- **Maven**: 3.6 或更高版本

## 🚀 快速部署

### 1. 数据库配置

```sql
-- 创建数据库
CREATE DATABASE codeduel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'codeduel'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON codeduel.* TO 'codeduel'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 后端服务部署

```bash
# 进入后端目录
cd code-fuel-backend

# 配置数据库连接
# 编辑 src/main/resources/application.yml
# 修改数据库连接信息

# 编译并启动
mvn clean compile
mvn spring-boot:run

# 或者打包部署
mvn clean package
java -jar target/codeduel-0.0.1-SNAPSHOT.jar
```

### 3. Python 评测服务部署

```bash
# 进入 Python 服务目录
cd code-fuel-backend/python-service

# 安装依赖
pip install -r requirements.txt

# 启动服务
python codeforces_api_service.py

# 或者使用批处理文件（Windows）
start_python_service.bat
```

### 4. 前端应用部署

```bash
# 进入前端目录
cd code-fuel-frontend

# 安装依赖
npm install

# 开发模式启动
npm run dev

# 生产环境构建
npm run build

# 预览构建结果
npm run preview
```

## 🔧 配置说明

### 后端配置文件

**application.yml** 主要配置项：

```yaml
# 数据库配置
spring:
  datasource:
    url: ************************************
    username: your_username
    password: your_password

# 服务端口
server:
  port: 8080

# 文件上传配置
file:
  upload:
    path: ./file/
```

### 前端配置文件

**vite.config.js** 主要配置项：

```javascript
export default defineConfig({
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

## 🐳 Docker 部署（推荐）

### 1. 创建 Docker Compose 文件

```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: codeduel
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  backend:
    build: ./code-fuel-backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    environment:
      SPRING_DATASOURCE_URL: ********************************

  python-service:
    build: ./code-fuel-backend/python-service
    ports:
      - "5000:5000"

  frontend:
    build: ./code-fuel-frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
```

### 2. 启动所有服务

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   - 检查 8080、5173、5000 端口是否被占用
   - 修改配置文件中的端口设置

2. **数据库连接失败**
   - 确认 MySQL 服务已启动
   - 检查数据库连接信息是否正确
   - 确认防火墙设置

3. **前端无法访问后端**
   - 检查代理配置是否正确
   - 确认后端服务已启动
   - 检查 CORS 配置

4. **Python 服务启动失败**
   - 确认 Python 版本兼容性
   - 检查依赖包是否正确安装
   - 查看错误日志

### 日志查看

```bash
# 后端日志
tail -f code-fuel-backend/logs/application.log

# 前端开发服务器日志
# 在终端中直接显示

# Python 服务日志
# 在终端中直接显示
```

## 📊 性能优化

### 数据库优化
- 创建适当的索引
- 定期清理过期数据
- 配置连接池参数

### 应用优化
- 启用 JVM 参数调优
- 配置缓存策略
- 使用 CDN 加速静态资源

### 监控建议
- 使用 Spring Boot Actuator 监控后端
- 配置日志收集和分析
- 设置性能指标监控

## 🔒 安全配置

### 生产环境安全
- 修改默认密码
- 启用 HTTPS
- 配置防火墙规则
- 定期更新依赖包

### 数据备份
- 定期备份数据库
- 备份用户上传文件
- 制定灾难恢复计划
