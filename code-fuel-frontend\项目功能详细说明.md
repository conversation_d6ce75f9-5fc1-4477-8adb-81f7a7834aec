# CodeDuel - 代码对战平台项目详细说明

## 项目概述

CodeDuel 是一个基于 Vue 3 + Element Plus 的在线代码对战平台，允许用户进行实时的编程竞赛。用户可以通过随机匹配或创建房间的方式与其他程序员进行对战，解决 Codeforces 平台上的编程题目。

## 技术栈

### 前端技术
- **Vue 3** - 主框架，使用 Composition API
- **Element Plus** - UI 组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理
- **Axios** - HTTP 请求库
- **Vite** - 构建工具
- **SCSS** - CSS 预处理器

### 主要依赖
```json
{
  "vue": "^3.3.4",
  "element-plus": "^2.10.4",
  "vue-router": "^4.2.5",
  "pinia": "^2.1.7",
  "axios": "^1.10.0",
  "echarts": "^5.6.0",
  "@vueup/vue-quill": "^1.2.0"
}
```

## 项目结构

```
src/
├── api/                    # API 接口定义
│   └── api.js             # 所有后端接口
├── assets/                # 静态资源
├── components/            # 公共组件
│   ├── BattleRoom.vue     # 对战房间组件
│   ├── RoomManager.vue    # 房间管理组件
│   ├── RoomWaiting.vue    # 房间等待组件
│   └── ...
├── router/                # 路由配置
│   └── index.js          # 路由定义和守卫
├── stores/               # 状态管理
│   └── userInfo.js       # 用户信息状态
├── utils/                # 工具函数
│   └── request.js        # Axios 配置
├── views/                # 页面组件
│   ├── dashboard/        # 主要功能页面
│   │   ├── Battle.vue    # 对战页面
│   │   ├── Room.vue      # 房间页面
│   │   ├── Home.vue      # 首页
│   │   ├── Forum.vue     # 论坛
│   │   ├── Ranking.vue   # 排行榜
│   │   └── ...
│   ├── admin/           # 管理员页面
│   └── user/            # 用户相关页面
├── App.vue              # 根组件
└── main.js              # 入口文件
```

## 核心功能模块

### 1. 用户认证系统
- **登录注册**: 支持 Codeforces 账号验证注册
- **用户信息管理**: 个人资料、头像、Rating 等
- **权限控制**: 普通用户和管理员权限区分

### 2. 对战系统 (核心功能)

#### 2.1 对战模式
- **随机匹配**: 系统自动匹配实力相当的对手
- **房间对战**: 创建房间或加入好友房间进行对战

#### 2.2 房间管理系统
房间管理是本项目的核心功能之一，实现了完整的房间生命周期管理。

##### 房间创建流程
1. **创建房间** (`RoomManager.vue` - `handleCreateRoom`)
   - 用户设置房间参数：
     - 难度范围 (800-3500 分)
     - 排除标签 (避免特定类型题目)
     - 题目选择模式 (随机/手动指定)
     - 房间描述
   - 调用 `createRoom` API 创建房间
   - 生成 6 位数字房间码
   - 自动跳转到房间页面

2. **加入房间** (`RoomManager.vue` - `handleJoinRoom`)
   - 输入 6 位房间码
   - 验证房间码格式和存在性
   - 检查房间是否已满
   - 调用 `joinRoom` API 加入房间

3. **房间列表** (`RoomManager.vue` - `loadActiveRooms`)
   - 显示所有活跃房间
   - 房间信息包括：房主、参与人数、创建时间、描述
   - 支持直接点击加入房间

##### 房间状态管理
房间具有以下状态：
- **WAITING**: 等待玩家加入
- **READY**: 人数已满，准备开始
- **BATTLING**: 对战进行中
- **FINISHED**: 对战已结束

##### 房间等待界面 (`RoomWaiting.vue`)
- **实时信息更新**: 每 3 秒自动刷新房间状态
- **参与者列表**: 显示所有参与者信息和 Rating
- **房主控制**: 房主可以开始对战
- **房间码分享**: 一键复制房间码到剪贴板
- **离开房间**: 支持主动离开，房主离开会解散房间

#### 2.3 对战界面 (`BattleRoom.vue`)
- **题目展示**: 显示 Codeforces 题目信息
- **外部链接**: 跳转到 Codeforces 做题
- **提交检查**: 检查用户在 Codeforces 的提交状态
- **实时计时**: 对战时间计时
- **胜负判定**: 
  - 首先通过题目者获胜
  - 支持主动投降
  - 退出房间判定为失败

### 3. 路由守卫系统
项目实现了智能的路由守卫机制 (`router/index.js`):

```javascript
router.beforeEach(async (to, from, next) => {
  // 检查用户当前房间状态
  const response = await getCurrentRoomStatus()
  
  if (response.status && response.data && response.data.hasRoom) {
    const userRoomCode = response.data.roomCode
    const expectedRoomPath = `/dashboard/battle/room/${userRoomCode}`
    
    // 如果用户在房间中，但访问的不是房间页面，则重定向到房间
    if (to.path !== expectedRoomPath) {
      next(expectedRoomPath)
      return
    }
  } else {
    // 用户不在房间中，但访问房间页面，重定向到对战页面
    if (to.path.startsWith('/dashboard/battle/room/')) {
      next('/dashboard/battle')
      return
    }
  }
  
  next()
})
```

这个守卫确保：
- 用户在房间中时，无论访问什么页面都会被重定向到对应的房间页面
- 用户不在房间中时，无法直接访问房间页面
- 防止用户意外离开房间导致对战中断

### 4. 论坛系统
- **帖子发布**: 支持 Markdown 编辑器
- **评论系统**: 多级评论回复
- **分页浏览**: 帖子列表分页显示

### 5. 排行榜系统
- **Rating 排名**: 基于对战结果的 Rating 计算
- **历史记录**: 详细的对战历史查看
- **统计数据**: 胜率、参与次数等统计

### 6. 聊天系统
- **实时聊天**: 全局聊天室
- **消息管理**: 发送、删除消息功能

### 7. 管理员系统
- **用户管理**: 用户列表、信息修改
- **数据管理**: 从 Codeforces 同步题目和标签数据
- **系统监控**: 数据更新状态监控

## 详细功能模块说明

### 1. 用户认证与注册系统

#### Codeforces 验证注册流程
**文件**: `src/views/Login.vue`

项目实现了独特的 Codeforces 账号验证注册机制：

1. **生成验证码**: 系统生成随机验证码
2. **用户设置**: 用户需要在 Codeforces 个人资料中设置验证码
3. **验证身份**: 系统通过 Codeforces API 验证用户身份
4. **完成注册**: 验证通过后创建账号

```javascript
// 生成验证码
const generateVerificationCode = () => request.post("/api/users/generate-verification-code")

// 验证Codeforces用户身份
const verifyCodeforcesUser = (data) => request.post("/api/users/verify-codeforces", data)

// 用户注册
const userRegister = (data) => request.post("/api/users/register", data)
```

#### 登录系统
- 支持用户名/密码登录
- 图形验证码防护
- 自动跳转到用户上次访问页面
- 登录状态持久化存储

### 2. 论坛系统详解

#### 帖子管理功能
**文件**: `src/views/dashboard/Forum.vue`

论坛系统提供完整的社区交流功能：

##### 帖子发布
- **Markdown 编辑器**: 支持实时预览和分屏编辑
- **富文本支持**: 标题、粗体、斜体、代码块、链接等
- **字数统计**: 实时显示内容字数
- **发布验证**: 标题和内容必填验证

```javascript
// 简单的 Markdown 渲染
const renderMarkdown = (text) => {
  return text
    .replace(/```([^`]+)```/gim, '<pre><code>$1</code></pre>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/gim, '<em>$1</em>')
    .replace(/`([^`]+)`/gim, '<code>$1</code>')
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank">$1</a>')
}
```

##### 帖子浏览
- **分页显示**: 支持分页浏览帖子列表
- **搜索功能**: 按标题模糊搜索
- **置顶帖子**: 重要帖子置顶显示
- **时间排序**: 按发布时间倒序排列

##### 评论系统
**文件**: `src/views/dashboard/PostDetail.vue`

- **多级评论**: 支持对帖子和评论的回复
- **实时加载**: 动态加载评论内容
- **权限控制**: 只有作者和管理员可以删除评论

```javascript
// 获取帖子评论
const getCommentsByPostId = (postId, params) => request.get(`/api/comments/post/${postId}`, { params })

// 发布评论
const createComment = (comment) => request.post("/api/comments/create", comment)

// 删除评论
const deleteComment = (commentId, params) => request.delete(`/api/comments/${commentId}`, { params })
```

### 3. 排行榜系统详解

#### Rating 计算系统
**文件**: `src/views/dashboard/Ranking.vue`

排行榜基于 ELO Rating 系统计算用户实力：

##### 排名展示
- **实时排名**: 基于最新 Rating 排序
- **详细信息**: 显示用户头像、ID、Rating、对战次数
- **历史记录**: 点击用户可查看详细对战历史
- **搜索功能**: 支持按用户名搜索

##### Rating 历史
```javascript
// 获取用户详细历史记录
const getUserDetailHistory = username => request.get(`/api/users/rating-history/${username}`)
```

用户可以查看：
- Rating 变化曲线图
- 每场对战的详细记录
- 胜率统计
- 对战对手信息

### 4. 聊天系统详解

#### 全局聊天室
**文件**: `src/views/dashboard/Chat.vue`

提供实时聊天功能：

##### 消息功能
- **发送消息**: 实时发送文本消息
- **消息历史**: 显示历史聊天记录
- **用户信息**: 显示发送者头像和用户名
- **时间戳**: 显示消息发送时间

```javascript
// 获取聊天消息列表
const getChatMessages = () => request.get("/api/chatMessages/getMessagesWithUsers")

// 发送聊天消息
const sendChatMessage = (message) => request.post("/api/chatMessages/addMessage", message)

// 删除聊天消息
const deleteChatMessage = (messageId, params) => request.delete(`/api/chatMessages/${messageId}`, { params })
```

##### 消息管理
- **删除权限**: 用户可以删除自己的消息
- **管理员权限**: 管理员可以删除任何消息
- **自动刷新**: 定时刷新获取新消息

### 5. 个人资料系统

#### 用户档案页面
**文件**: `src/views/dashboard/Profile.vue`

用户个人资料展示：

##### 基本信息
- **头像显示**: 用户头像和基本信息
- **Codeforces 信息**: 显示 CF 用户名和 Rating
- **平台统计**: 显示在平台上的对战记录

##### 对战历史
- **历史记录**: 显示用户所有对战记录
- **胜负统计**: 胜率、总场次等统计信息
- **Rating 变化**: Rating 历史变化图表

### 6. 首页统计系统

#### 数据展示
**文件**: `src/views/dashboard/Home.vue`

首页提供平台整体数据概览：

##### 统计卡片
- **注册用户数**: 平台总用户数
- **总对战数**: 平台总对战场次
- **题目总数**: 可用题目数量
- **发帖总数**: 论坛帖子总数

```javascript
// 获取首页统计数据
const getHomeStats = () => request.get("/api/users/home-stats")

// 获取最近对战记录
const getRecentBattleRecords = params => request.get("/api/battleRecords/recent", { params })
```

##### 功能预览
- **排行榜 Top 10**: 显示前10名用户
- **最近对战**: 显示最新的对战记录
- **快速入口**: 提供各功能模块的快速访问

## 技术实现细节

### 1. 组件架构设计

#### 组件层次结构
```
App.vue (根组件)
├── Dashboard.vue (主布局)
│   ├── Home.vue (首页)
│   ├── Battle.vue (对战页面)
│   │   ├── RoomManager.vue (房间管理)
│   │   ├── RoomWaiting.vue (房间等待)
│   │   └── BattleRoom.vue (对战界面)
│   ├── Forum.vue (论坛)
│   ├── Ranking.vue (排行榜)
│   ├── Chat.vue (聊天)
│   └── Profile.vue (个人资料)
├── Admin.vue (管理员界面)
└── Login.vue (登录页面)
```

#### 组件通信机制

##### 父子组件通信
```javascript
// 父组件向子组件传递数据
<RoomWaiting
  :visible="showRoomWaiting"
  :room-info="currentRoom"
  @close="handleRoomWaitingClose"
  @battle-start="handleBattleStart"
/>

// 子组件接收 props 和发送事件
const props = defineProps({
  roomInfo: { type: Object, required: false },
  visible: { type: Boolean, default: false }
})

const emit = defineEmits(['close', 'battle-start', 'room-update'])
```

##### 状态管理 (Pinia)
```javascript
// stores/userInfo.js
export const useUserInfoStore = defineStore('userInfo', () => {
  const userInfo = ref({})

  const setUserInfo = (newUserInfo) => {
    userInfo.value = newUserInfo
  }

  const removeUserInfo = () => {
    userInfo.value = {}
  }

  return { userInfo, setUserInfo, removeUserInfo }
}, {
  persist: true  // 数据持久化
})
```

### 2. 路由系统设计

#### 路由配置
**文件**: `src/router/index.js`

```javascript
const routes = [
  { path: '/', redirect: '/dashboard/home' },
  { path: '/login', component: Login },
  {
    path: '/dashboard',
    component: Dashboard,
    children: [
      { path: 'home', component: Home },
      { path: 'forum', component: Forum },
      { path: 'forum/post/:id', component: PostDetail },
      { path: 'battle', component: Battle },
      { path: 'battle/room/:roomid', component: Room },
      { path: 'ranking', component: Ranking },
      { path: 'chat', component: Chat },
      { path: 'profile/:username', component: Profile }
    ]
  }
]
```

#### 智能路由守卫
路由守卫确保用户在房间中时不会意外离开：

```javascript
router.beforeEach(async (to, from, next) => {
  try {
    // 检查用户当前房间状态
    const response = await getCurrentRoomStatus()

    if (response.status && response.data && response.data.hasRoom) {
      const userRoomCode = response.data.roomCode
      const expectedRoomPath = `/dashboard/battle/room/${userRoomCode}`

      // 如果用户在房间中，但访问的不是房间页面，则重定向到房间
      if (to.path !== expectedRoomPath) {
        console.log(`🔄 用户在房间 ${userRoomCode} 中，重定向到房间页面`)
        next(expectedRoomPath)
        return
      }
    } else {
      // 用户不在房间中，但访问房间页面，重定向到对战页面
      if (to.path.startsWith('/dashboard/battle/room/')) {
        console.log('🔄 用户不在房间中，重定向到对战页面')
        next('/dashboard/battle')
        return
      }
    }

    next()
  } catch (error) {
    console.error('❌ 路由守卫检查失败:', error)
    next() // 出错时允许正常导航
  }
})
```

### 3. HTTP 请求封装

#### Axios 拦截器配置
**文件**: `src/utils/request.js`

```javascript
import axios from "axios"
import { ElMessage } from "element-plus"
import router from "@/router"

const instance = axios.create()

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 可以在这里添加 token 等认证信息
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
instance.interceptors.response.use(
  result => {
    // 后端使用 status 字段表示成功/失败
    if (result.data.status === true) {
      return result.data
    }
    ElMessage.error(result.data.message || '服务异常')
    return Promise.reject(result.data)
  },
  error => {
    if (error.response?.status === 70005) {
      ElMessage.error("请先登录")
      router.push('/login')
    } else {
      ElMessage.error('服务异常')
    }
    return Promise.reject(error)
  }
)
```

#### API 接口统一管理
**文件**: `src/api/api.js`

所有后端接口统一在此文件中定义，便于维护：

```javascript
// 用户相关
export const usersLogin = users => request.post("/api/users/login", users)
export const getInfo = () => request.get("/api/auth/getUserInfo")
export const getUserByUsername = username => request.get(`/api/users/profile/${username}`)

// 房间相关
export const createRoom = data => request.post("/api/room/create", data)
export const joinRoom = data => request.post("/api/room/join", data)
export const getRoomInfo = roomCode => request.get(`/api/room/${roomCode}`)

// 对战相关
export const checkSubmissionStatus = data => request.post("/api/battle/check-submission", data)
export const finishBattle = data => request.post("/api/room/finish-battle", data)
```

### 4. 样式系统设计

#### SCSS 预处理器
项目使用 SCSS 进行样式管理，支持变量、嵌套、混入等特性：

```scss
// 主题色彩变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;

// 组件样式示例
.battle-container {
  max-width: 1400px;
  margin: 0 auto;

  .mode-selection {
    .mode-card {
      cursor: pointer;
      transition: transform 0.3s;

      &:hover {
        transform: translateY(-5px);
      }

      .mode-content {
        text-align: center;
        padding: 20px;

        .mode-icon {
          font-size: 3em;
          color: $primary-color;
          margin-bottom: 15px;
        }
      }
    }
  }
}
```

#### 响应式设计
使用 Element Plus 的栅格系统实现响应式布局：

```vue
<el-row :gutter="20">
  <el-col :span="12" :md="8" :sm="24">
    <!-- 内容 -->
  </el-col>
</el-row>
```

### 5. 性能优化策略

#### 组件懒加载
路由组件采用动态导入实现懒加载：

```javascript
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/views/Dashboard.vue'),
    children: [
      { path: 'home', component: () => import('@/views/dashboard/Home.vue') },
      { path: 'battle', component: () => import('@/views/dashboard/Battle.vue') }
    ]
  }
]
```

#### 数据缓存
使用 Pinia 的持久化插件缓存用户数据：

```javascript
const pinia = createPinia()
const persistedState = createPersistedState()
pinia.use(persistedState)
```

#### 防抖和节流
在搜索等场景使用防抖优化性能：

```javascript
import { debounce } from 'lodash-es'

const debouncedSearch = debounce((keyword) => {
  performSearch(keyword)
}, 300)
```

## 项目部署和构建

### 开发环境启动
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 生产环境构建
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 项目配置
**文件**: `vite.config.js`

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

## 总结

CodeDuel 项目实现了一个完整的在线代码对战平台，其中房间系统是核心功能。通过精心设计的组件架构和状态管理，实现了流畅的用户体验和稳定的功能表现。

### 项目亮点：
1. **完整的房间生命周期管理**: 从创建到结束的全流程控制
2. **智能路由守卫**: 防止用户意外离开房间
3. **实时状态同步**: 确保所有用户看到一致的房间状态
4. **灵活的题目筛选**: 支持多种条件的题目选择
5. **完善的错误处理**: 友好的错误提示和异常恢复
6. **响应式设计**: 适配不同屏幕尺寸的设备

### 技术特色：
1. **组件化架构**: 功能模块清晰分离，便于维护
2. **状态管理**: 使用 Pinia 进行集中状态管理
3. **API 设计**: RESTful 风格的接口设计
4. **用户体验**: 丰富的交互反馈和状态提示
5. **性能优化**: 懒加载、缓存、防抖等优化策略

### 开房间功能核心实现：
1. **创建房间**: 用户设置参数 → 调用API → 生成房间码 → 跳转房间页面
2. **加入房间**: 输入房间码 → 验证格式 → 调用API → 加入成功跳转
3. **房间等待**: 实时刷新状态 → 显示参与者 → 房主开始对战
4. **对战进行**: 显示题目 → 检查提交 → 判定胜负 → 结束对战
5. **状态管理**: 路由守卫保护 → 实时同步 → 异常处理

项目代码结构清晰，组件职责明确，具有良好的可维护性和扩展性。整个房间系统的实现体现了现代前端开发的最佳实践，包括组件化设计、状态管理、路由控制、错误处理等方面。

## 技术实现细节

### 1. 组件架构设计

#### 组件层次结构
```
App.vue (根组件)
├── Dashboard.vue (主布局)
│   ├── Home.vue (首页)
│   ├── Battle.vue (对战页面)
│   │   ├── RoomManager.vue (房间管理)
│   │   ├── RoomWaiting.vue (房间等待)
│   │   └── BattleRoom.vue (对战界面)
│   ├── Forum.vue (论坛)
│   ├── Ranking.vue (排行榜)
│   ├── Chat.vue (聊天)
│   └── Profile.vue (个人资料)
├── Admin.vue (管理员界面)
└── Login.vue (登录页面)
```

#### 组件通信机制

##### 父子组件通信
```javascript
// 父组件向子组件传递数据
<RoomWaiting
  :visible="showRoomWaiting"
  :room-info="currentRoom"
  @close="handleRoomWaitingClose"
  @battle-start="handleBattleStart"
/>

// 子组件接收 props 和发送事件
const props = defineProps({
  roomInfo: { type: Object, required: false },
  visible: { type: Boolean, default: false }
})

const emit = defineEmits(['close', 'battle-start', 'room-update'])
```

##### 状态管理 (Pinia)
```javascript
// stores/userInfo.js
export const useUserInfoStore = defineStore('userInfo', () => {
  const userInfo = ref({})

  const setUserInfo = (newUserInfo) => {
    userInfo.value = newUserInfo
  }

  const removeUserInfo = () => {
    userInfo.value = {}
  }

  return { userInfo, setUserInfo, removeUserInfo }
}, {
  persist: true  // 数据持久化
})
```

### 2. 路由系统设计

#### 路由配置
**文件**: `src/router/index.js`

```javascript
const routes = [
  { path: '/', redirect: '/dashboard/home' },
  { path: '/login', component: Login },
  {
    path: '/dashboard',
    component: Dashboard,
    children: [
      { path: 'home', component: Home },
      { path: 'forum', component: Forum },
      { path: 'forum/post/:id', component: PostDetail },
      { path: 'battle', component: Battle },
      { path: 'battle/room/:roomid', component: Room },
      { path: 'ranking', component: Ranking },
      { path: 'chat', component: Chat },
      { path: 'profile/:username', component: Profile }
    ]
  }
]
```

#### 智能路由守卫
路由守卫确保用户在房间中时不会意外离开：

```javascript
router.beforeEach(async (to, from, next) => {
  try {
    // 检查用户当前房间状态
    const response = await getCurrentRoomStatus()

    if (response.status && response.data && response.data.hasRoom) {
      const userRoomCode = response.data.roomCode
      const expectedRoomPath = `/dashboard/battle/room/${userRoomCode}`

      // 如果用户在房间中，但访问的不是房间页面，则重定向到房间
      if (to.path !== expectedRoomPath) {
        console.log(`🔄 用户在房间 ${userRoomCode} 中，重定向到房间页面`)
        next(expectedRoomPath)
        return
      }
    } else {
      // 用户不在房间中，但访问房间页面，重定向到对战页面
      if (to.path.startsWith('/dashboard/battle/room/')) {
        console.log('🔄 用户不在房间中，重定向到对战页面')
        next('/dashboard/battle')
        return
      }
    }

    next()
  } catch (error) {
    console.error('❌ 路由守卫检查失败:', error)
    next() // 出错时允许正常导航
  }
})
```

### 3. HTTP 请求封装

#### Axios 拦截器配置
**文件**: `src/utils/request.js`

```javascript
import axios from "axios"
import { ElMessage } from "element-plus"
import router from "@/router"

const instance = axios.create()

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 可以在这里添加 token 等认证信息
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
instance.interceptors.response.use(
  result => {
    // 后端使用 status 字段表示成功/失败
    if (result.data.status === true) {
      return result.data
    }
    ElMessage.error(result.data.message || '服务异常')
    return Promise.reject(result.data)
  },
  error => {
    if (error.response?.status === 70005) {
      ElMessage.error("请先登录")
      router.push('/login')
    } else {
      ElMessage.error('服务异常')
    }
    return Promise.reject(error)
  }
)
```

#### API 接口统一管理
**文件**: `src/api/api.js`

所有后端接口统一在此文件中定义，便于维护：

```javascript
// 用户相关
export const usersLogin = users => request.post("/api/users/login", users)
export const getInfo = () => request.get("/api/auth/getUserInfo")
export const getUserByUsername = username => request.get(`/api/users/profile/${username}`)

// 房间相关
export const createRoom = data => request.post("/api/room/create", data)
export const joinRoom = data => request.post("/api/room/join", data)
export const getRoomInfo = roomCode => request.get(`/api/room/${roomCode}`)

// 对战相关
export const checkSubmissionStatus = data => request.post("/api/battle/check-submission", data)
export const finishBattle = data => request.post("/api/room/finish-battle", data)
```

### 4. 样式系统设计

#### SCSS 预处理器
项目使用 SCSS 进行样式管理，支持变量、嵌套、混入等特性：

```scss
// 主题色彩变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;

// 组件样式示例
.battle-container {
  max-width: 1400px;
  margin: 0 auto;

  .mode-selection {
    .mode-card {
      cursor: pointer;
      transition: transform 0.3s;

      &:hover {
        transform: translateY(-5px);
      }

      .mode-content {
        text-align: center;
        padding: 20px;

        .mode-icon {
          font-size: 3em;
          color: $primary-color;
          margin-bottom: 15px;
        }
      }
    }
  }
}
```

#### 响应式设计
使用 Element Plus 的栅格系统实现响应式布局：

```vue
<el-row :gutter="20">
  <el-col :span="12" :md="8" :sm="24">
    <!-- 内容 -->
  </el-col>
</el-row>
```

### 5. 性能优化策略

#### 组件懒加载
路由组件采用动态导入实现懒加载：

```javascript
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/views/Dashboard.vue'),
    children: [
      { path: 'home', component: () => import('@/views/dashboard/Home.vue') },
      { path: 'battle', component: () => import('@/views/dashboard/Battle.vue') }
    ]
  }
]
```

#### 数据缓存
使用 Pinia 的持久化插件缓存用户数据：

```javascript
const pinia = createPinia()
const persistedState = createPersistedState()
pinia.use(persistedState)
```

#### 防抖和节流
在搜索等场景使用防抖优化性能：

```javascript
import { debounce } from 'lodash-es'

const debouncedSearch = debounce((keyword) => {
  performSearch(keyword)
}, 300)
```

## 开房间功能实现详解

### 核心组件交互流程

1. **Battle.vue** (对战主页面)
   - 提供"创建房间"、"加入房间"、"房间列表"三个入口
   - 引用 `RoomManager` 组件处理房间操作
   - 监听房间操作结果，成功后跳转到房间页面

2. **RoomManager.vue** (房间管理组件)
   - 处理所有房间相关的弹窗和表单
   - 创建房间：收集用户设置，调用后端 API
   - 加入房间：验证房间码，调用后端 API
   - 房间列表：展示活跃房间，支持快速加入

3. **Room.vue** (房间页面)
   - 根据房间状态显示不同界面
   - WAITING/READY 状态显示 `RoomWaiting` 组件
   - BATTLING 状态显示 `BattleRoom` 组件
   - 处理用户重新加入房间的逻辑

4. **RoomWaiting.vue** (房间等待界面)
   - 实时显示房间信息和参与者列表
   - 房主可以开始对战
   - 所有用户可以离开房间

5. **BattleRoom.vue** (对战界面)
   - 显示题目信息和对战状态
   - 处理提交检查和胜负判定
   - 支持投降和退出功能

### API 接口设计

房间相关的主要 API 接口：

```javascript
// 创建房间
export const createRoom = data => request.post("/api/room/create", data)

// 加入房间  
export const joinRoom = data => request.post("/api/room/join", data)

// 离开房间
export const leaveRoom = data => request.post("/api/room/leave", data)

// 获取房间信息
export const getRoomInfo = roomCode => request.get(`/api/room/${roomCode}`)

// 开始对战
export const startBattle = roomCode => request.post(`/api/room/${roomCode}/start`)

// 获取活跃房间列表
export const getActiveRooms = () => request.get("/api/room/active")

// 获取用户当前房间状态
export const getCurrentRoomStatus = () => request.get("/api/user/current-room")

// 重新加入房间
export const rejoinRoom = roomCode => request.post("/api/user/rejoin-room", { roomCode })
```

### 状态管理

项目使用 Pinia 进行状态管理，主要管理用户信息：

```javascript
export const useUserInfoStore = defineStore('userInfo', () => {
  const userInfo = ref({})
  
  const setUserInfo = (newUserInfo) => {
    userInfo.value = newUserInfo
  }
  
  const removeUserInfo = () => {
    userInfo.value = {}
  }
  
  return { userInfo, setUserInfo, removeUserInfo }
}, {
  persist: true  // 持久化存储
})
```

## 项目特色功能

1. **智能路由守卫**: 确保用户在房间中时不会意外离开
2. **实时状态同步**: 房间状态实时更新，确保所有用户看到一致的信息
3. **灵活的题目选择**: 支持难度范围、标签排除等多种筛选条件
4. **完整的对战流程**: 从创建房间到对战结束的完整闭环
5. **用户友好的界面**: 清晰的状态提示和操作引导
6. **错误处理机制**: 完善的错误提示和异常处理

## 开房间功能详细实现流程

### 1. 创建房间完整流程

#### 步骤1: 用户点击"创建房间"按钮
**文件**: `src/views/dashboard/Battle.vue`
```javascript
const openCreateRoom = () => {
  if (roomManagerRef.value) {
    roomManagerRef.value.openCreateDialog()
  }
}
```

#### 步骤2: 打开创建房间弹窗
**文件**: `src/components/RoomManager.vue`
```javascript
const openCreateDialog = () => {
  resetCreateForm()        // 重置表单
  loadAllTags()           // 加载标签数据
  createDialogVisible.value = true
}
```

#### 步骤3: 用户填写房间设置
用户可以设置以下参数：
- **难度范围**: 最小难度到最大难度 (800-3500分)
- **排除标签**: 选择不想要的题目类型
- **题目选择模式**:
  - 随机选择：根据条件随机选题
  - 手动指定：输入具体题目ID
- **房间描述**: 自定义房间说明

#### 步骤4: 提交创建请求
**文件**: `src/components/RoomManager.vue`
```javascript
const handleCreateRoom = async () => {
  // 构建请求数据
  const createData = {
    creatorId: currentUser.value.id,
    creatorName: currentUser.value.codeforcesId,
    problemId: createForm.value.problemId || null,
    description: createForm.value.description || '欢迎来到我的房间',
    minDifficulty: createForm.value.minDifficulty,
    maxDifficulty: createForm.value.maxDifficulty,
    excludedTags: createForm.value.excludedTags
  }

  // 调用API创建房间
  const response = await createRoom(createData)

  if (response.status) {
    // 触发房间创建成功事件
    emit('room-created', response.data)
    // 关闭弹窗并重置表单
    createDialogVisible.value = false
    resetCreateForm()
  }
}
```

#### 步骤5: 跳转到房间页面
**文件**: `src/views/dashboard/Battle.vue`
```javascript
const handleRoomCreated = (roomInfo) => {
  ElMessage({
    message: `房间创建成功！房间码: ${roomInfo.roomCode}`,
    type: 'success',
    duration: 5000
  })

  // 跳转到房间页面
  router.push(`/dashboard/battle/room/${roomInfo.roomCode}`)
}
```

### 2. 加入房间完整流程

#### 步骤1: 用户选择加入方式
有三种加入房间的方式：
1. **输入房间码**: 手动输入6位数字房间码
2. **房间列表**: 从活跃房间列表中选择
3. **直接链接**: 通过房间链接直接访问

#### 步骤2: 验证和加入
**文件**: `src/components/RoomManager.vue`
```javascript
const handleJoinRoom = async () => {
  // 验证房间码格式（6位数字）
  const roomCode = parseInt(joinForm.value.roomCode.trim())
  if (isNaN(roomCode) || roomCode < 100000 || roomCode > 999999) {
    ElMessage.warning('房间码格式不正确，请输入6位数字')
    return
  }

  // 构建加入房间请求数据
  const joinData = {
    roomCode: roomCode,
    userId: currentUser.value.id,
    userName: currentUser.value.codeforcesId
  }

  // 调用API加入房间
  const response = await joinRoom(joinData)

  if (response.status) {
    emit('room-joined', response.data)
    joinDialogVisible.value = false
    resetJoinForm()
  }
}
```

### 3. 房间状态管理系统

#### 房间状态枚举
```javascript
const ROOM_STATUS = {
  WAITING: 'WAITING',     // 等待玩家加入
  READY: 'READY',         // 准备就绪，可以开始
  BATTLING: 'BATTLING',   // 对战进行中
  FINISHED: 'FINISHED'    // 对战已结束
}
```

#### 状态转换流程
1. **WAITING** → **READY**: 当房间人数达到最大值时
2. **READY** → **BATTLING**: 房主点击"开始对战"
3. **BATTLING** → **FINISHED**: 对战结束（有人获胜或投降）

#### 实时状态同步
**文件**: `src/components/RoomWaiting.vue`
```javascript
const refreshRoomInfo = async () => {
  const response = await getRoomInfo(localRoomInfo.value.roomCode)

  if (response.status && response.data) {
    const oldStatus = localRoomInfo.value.status
    localRoomInfo.value = response.data

    // 如果状态发生变化，通知父组件
    if (oldStatus !== response.data.status) {
      emit('room-update', response.data)

      // 如果对战开始，自动跳转
      if (response.data.status === 'BATTLING') {
        emit('battle-start', response.data)
      }
    }
  }
}

// 每3秒自动刷新房间信息
onMounted(() => {
  refreshTimer = setInterval(refreshRoomInfo, 3000)
})
```

### 4. 对战系统实现

#### 对战开始流程
**文件**: `src/components/RoomWaiting.vue`
```javascript
const handleStartBattle = async () => {
  if (!canStartBattle.value) {
    ElMessage.warning('当前无法开始对战')
    return
  }

  const response = await startBattle(localRoomInfo.value.roomCode)

  if (response.status && response.data) {
    ElMessage.success('对战开始！')
    localRoomInfo.value = response.data
    emit('battle-start', response.data)
  }
}
```

#### 提交状态检查
**文件**: `src/components/BattleRoom.vue`
```javascript
const checkSubmission = async () => {
  const response = await checkSubmissionStatus({
    codeforcesHandle: currentUser.value.codeforcesId,
    problemId: problem.value.problemId
  })

  if (response.status) {
    const isPassed = response.data.isPassed
    submissionStatus.value.isPassed = isPassed
    submissionStatus.value.lastCheck = new Date()

    if (isPassed) {
      ElMessage.success('恭喜！您已通过该题目！')
      await handleBattleWin()
    }
  }
}
```

#### 对战结束处理
```javascript
const handleBattleWin = async () => {
  battleStatus.value = 'FINISHED'
  stopBattleTimer()

  const response = await finishBattle({
    roomCode: props.roomInfo.roomCode,
    winnerId: currentUser.value.id,
    battleTime: battleTime.value,
    reason: 'SOLVED'
  })

  if (response.status) {
    emit('battle-end', {
      winner: currentUser.value,
      loser: opponent.value,
      battleTime: battleTime.value,
      roomInfo: props.roomInfo,
      reason: 'SOLVED'
    })
  }
}
```

### 5. 错误处理和用户体验优化

#### 网络错误处理
**文件**: `src/utils/request.js`
```javascript
instance.interceptors.response.use(
  result => {
    if (result.data.status === true) {
      return result.data
    }
    ElMessage.error(result.data.message ? result.data.message : '服务异常')
    return Promise.reject(result.data)
  },
  error => {
    if (error.response && error.response.status === 70005) {
      ElMessage.error("请先登录")
      router.push('/login')
    } else {
      ElMessage.error('服务异常')
    }
    return Promise.reject(error)
  }
)
```

#### 用户操作确认
```javascript
const handleLeaveRoom = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要离开房间吗？',
      '确认离开',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 执行离开房间逻辑
    const response = await leaveRoom({
      roomCode: localRoomInfo.value.roomCode,
      userId: userInfoStore.userInfo.id
    })

    if (response.status) {
      ElMessage.success('已离开房间')
      emit('close')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('离开房间失败: ' + error.message)
    }
  }
}
```

## 总结

CodeDuel 项目实现了一个完整的在线代码对战平台，其中房间系统是核心功能。通过精心设计的组件架构和状态管理，实现了流畅的用户体验和稳定的功能表现。

### 项目亮点：
1. **完整的房间生命周期管理**: 从创建到结束的全流程控制
2. **智能路由守卫**: 防止用户意外离开房间
3. **实时状态同步**: 确保所有用户看到一致的房间状态
4. **灵活的题目筛选**: 支持多种条件的题目选择
5. **完善的错误处理**: 友好的错误提示和异常恢复
6. **响应式设计**: 适配不同屏幕尺寸的设备

### 技术特色：
1. **组件化架构**: 功能模块清晰分离，便于维护
2. **状态管理**: 使用 Pinia 进行集中状态管理
3. **API 设计**: RESTful 风格的接口设计
4. **用户体验**: 丰富的交互反馈和状态提示

项目代码结构清晰，组件职责明确，具有良好的可维护性和扩展性。
