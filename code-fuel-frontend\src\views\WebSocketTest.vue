<template>
  <div class="websocket-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>WebSocket 连接测试</h2>
          <el-tag :type="connectionStatus.type" size="large">
            {{ connectionStatus.text }}
          </el-tag>
        </div>
      </template>

      <!-- 连接控制 -->
      <div class="connection-controls">
        <el-button
          type="primary"
          @click="connectWebSocket"
          :loading="connecting"
          :disabled="connected"
        >
          连接 WebSocket
        </el-button>
        
        <el-button
          type="danger"
          @click="disconnectWebSocket"
          :disabled="!connected"
        >
          断开连接
        </el-button>
        
        <el-button
          @click="clearLogs"
          :icon="Delete"
        >
          清空日志
        </el-button>
      </div>

      <!-- 连接信息 -->
      <div class="connection-info" v-if="wsStatus">
        <el-descriptions title="连接状态" :column="2" border>
          <el-descriptions-item label="连接状态">
            <el-tag :type="wsStatus.connected ? 'success' : 'danger'">
              {{ wsStatus.connected ? '已连接' : '未连接' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="连接中">
            <el-tag :type="wsStatus.connecting ? 'warning' : 'info'">
              {{ wsStatus.connecting ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="重连次数">
            {{ wsStatus.reconnectAttempts }}
          </el-descriptions-item>
          <el-descriptions-item label="订阅数量">
            {{ wsStatus.subscriptions }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 房间测试 -->
      <div class="room-test" v-if="connected">
        <el-divider content-position="left">房间功能测试</el-divider>
        
        <div class="test-controls">
          <el-input
            v-model="testRoomCode"
            placeholder="输入房间码进行测试"
            style="width: 200px; margin-right: 10px;"
          />
          
          <el-button
            type="success"
            @click="subscribeToRoom"
            :disabled="!testRoomCode || subscribedRooms.has(testRoomCode)"
          >
            订阅房间
          </el-button>
          
          <el-button
            type="warning"
            @click="unsubscribeFromRoom"
            :disabled="!testRoomCode || !subscribedRooms.has(testRoomCode)"
          >
            取消订阅
          </el-button>
        </div>

        <div class="subscribed-rooms" v-if="subscribedRooms.size > 0">
          <h4>已订阅房间：</h4>
          <el-tag
            v-for="roomCode in Array.from(subscribedRooms)"
            :key="roomCode"
            closable
            @close="unsubscribeFromRoom(roomCode)"
            style="margin-right: 10px; margin-bottom: 10px;"
          >
            房间 {{ roomCode }}
          </el-tag>
        </div>

        <!-- 消息发送测试 -->
        <div class="message-test">
          <h4>消息发送测试：</h4>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-select v-model="testMessage.type" placeholder="消息类型">
                <el-option label="加入房间" value="join" />
                <el-option label="离开房间" value="leave" />
                <el-option label="开始对战" value="start" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input v-model="testMessage.roomCode" placeholder="房间码" />
            </el-col>
            <el-col :span="8">
              <el-input v-model="testMessage.content" placeholder="消息内容" />
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="sendTestMessage">
                发送消息
              </el-button>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 消息日志 -->
      <div class="message-logs">
        <el-divider content-position="left">消息日志</el-divider>
        
        <div class="log-controls">
          <el-checkbox v-model="autoScroll">自动滚动</el-checkbox>
          <el-checkbox v-model="showTimestamp">显示时间戳</el-checkbox>
          <span class="log-count">共 {{ logs.length }} 条消息</span>
        </div>

        <div class="logs-container" ref="logsContainer">
          <div
            v-for="log in logs"
            :key="log.id"
            class="log-item"
            :class="log.type"
          >
            <div class="log-header">
              <el-tag :type="getLogTagType(log.type)" size="small">
                {{ log.type.toUpperCase() }}
              </el-tag>
              <span v-if="showTimestamp" class="log-time">
                {{ formatTime(log.timestamp) }}
              </span>
            </div>
            <div class="log-content">
              <pre>{{ log.message }}</pre>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { webSocketService } from '@/utils/websocket'
import { useUserInfoStore } from '@/stores/userInfo'

// ==================== 响应式数据 ====================

const userInfoStore = useUserInfoStore()

// 连接状态
const connected = ref(false)
const connecting = ref(false)
const wsStatus = ref(null)

// 测试数据
const testRoomCode = ref('')
const subscribedRooms = ref(new Set())

// 测试消息
const testMessage = ref({
  type: 'join',
  roomCode: '',
  content: ''
})

// 日志相关
const logs = ref([])
const autoScroll = ref(true)
const showTimestamp = ref(true)
const logsContainer = ref(null)

// 当前用户
const currentUser = computed(() => userInfoStore.userInfo)

// 连接状态显示
const connectionStatus = computed(() => {
  if (connecting.value) {
    return { type: 'warning', text: '连接中...' }
  } else if (connected.value) {
    return { type: 'success', text: '已连接' }
  } else {
    return { type: 'danger', text: '未连接' }
  }
})

// ==================== WebSocket 方法 ====================

/**
 * 连接WebSocket
 */
const connectWebSocket = async () => {
  connecting.value = true
  addLog('info', '开始连接WebSocket...')

  try {
    await webSocketService.connect()
    connected.value = true
    updateStatus()
    
    addLog('success', 'WebSocket连接成功')
    ElMessage.success('WebSocket连接成功')
    
  } catch (error) {
    addLog('error', `WebSocket连接失败: ${error.message}`)
    ElMessage.error('WebSocket连接失败')
  } finally {
    connecting.value = false
  }
}

/**
 * 断开WebSocket连接
 */
const disconnectWebSocket = () => {
  webSocketService.disconnect()
  connected.value = false
  subscribedRooms.value.clear()
  updateStatus()
  
  addLog('info', 'WebSocket连接已断开')
  ElMessage.info('WebSocket连接已断开')
}

/**
 * 更新连接状态
 */
const updateStatus = () => {
  wsStatus.value = webSocketService.getStatus()
}

/**
 * 订阅房间
 */
const subscribeToRoom = (roomCode = testRoomCode.value) => {
  if (!roomCode) {
    ElMessage.warning('请输入房间码')
    return
  }

  if (subscribedRooms.value.has(roomCode)) {
    ElMessage.warning('已经订阅了该房间')
    return
  }

  const subscription = webSocketService.subscribeToRoom(roomCode, (message) => {
    addLog('receive', `房间 ${roomCode} 收到消息: ${JSON.stringify(message, null, 2)}`)
  })

  if (subscription) {
    subscribedRooms.value.add(roomCode)
    addLog('info', `已订阅房间 ${roomCode}`)
    ElMessage.success(`已订阅房间 ${roomCode}`)
  } else {
    addLog('error', `订阅房间 ${roomCode} 失败`)
    ElMessage.error(`订阅房间 ${roomCode} 失败`)
  }
}

/**
 * 取消订阅房间
 */
const unsubscribeFromRoom = (roomCode = testRoomCode.value) => {
  if (!roomCode) {
    ElMessage.warning('请输入房间码')
    return
  }

  webSocketService.unsubscribeFromRoom(roomCode)
  subscribedRooms.value.delete(roomCode)
  
  addLog('info', `已取消订阅房间 ${roomCode}`)
  ElMessage.success(`已取消订阅房间 ${roomCode}`)
}

/**
 * 发送测试消息
 */
const sendTestMessage = () => {
  const { type, roomCode, content } = testMessage.value

  if (!roomCode) {
    ElMessage.warning('请输入房间码')
    return
  }

  let destination = ''
  let body = {
    userId: currentUser.value?.id || 'test-user',
    userName: currentUser.value?.codeforcesId || 'test-user'
  }

  switch (type) {
    case 'join':
      destination = `/app/room/${roomCode}/join`
      break
    case 'leave':
      destination = `/app/room/${roomCode}/leave`
      break
    case 'start':
      destination = `/app/room/${roomCode}/start`
      break
    case 'custom':
      destination = `/app/test`
      body.message = content
      break
    default:
      ElMessage.warning('请选择消息类型')
      return
  }

  const success = webSocketService.sendMessage(destination, body)
  
  if (success) {
    addLog('send', `发送消息到 ${destination}: ${JSON.stringify(body, null, 2)}`)
    ElMessage.success('消息发送成功')
  } else {
    addLog('error', `发送消息失败: ${destination}`)
    ElMessage.error('消息发送失败')
  }
}

// ==================== 日志方法 ====================

/**
 * 添加日志
 */
const addLog = (type, message) => {
  const log = {
    id: Date.now() + Math.random(),
    type,
    message,
    timestamp: Date.now()
  }

  logs.value.unshift(log)

  // 限制日志数量
  if (logs.value.length > 1000) {
    logs.value = logs.value.slice(0, 1000)
  }

  // 自动滚动
  if (autoScroll.value) {
    nextTick(() => {
      if (logsContainer.value) {
        logsContainer.value.scrollTop = 0
      }
    })
  }
}

/**
 * 清空日志
 */
const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清空')
}

/**
 * 获取日志标签类型
 */
const getLogTagType = (type) => {
  switch (type) {
    case 'success':
      return 'success'
    case 'error':
      return 'danger'
    case 'send':
      return 'primary'
    case 'receive':
      return 'success'
    case 'info':
    default:
      return 'info'
  }
}

/**
 * 格式化时间
 */
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  })
}

// ==================== 生命周期 ====================

onMounted(() => {
  // 检查当前连接状态
  connected.value = webSocketService.isConnected()
  updateStatus()
  
  addLog('info', 'WebSocket测试页面已加载')
})

onUnmounted(() => {
  // 清理订阅
  subscribedRooms.value.forEach(roomCode => {
    webSocketService.unsubscribeFromRoom(roomCode)
  })
})
</script>

<style scoped lang="scss">
.websocket-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .test-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
      }
    }

    .connection-controls {
      margin-bottom: 20px;

      .el-button {
        margin-right: 10px;
      }
    }

    .connection-info {
      margin-bottom: 20px;
    }

    .room-test {
      margin-bottom: 20px;

      .test-controls {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }

      .subscribed-rooms {
        margin-bottom: 15px;

        h4 {
          margin-bottom: 10px;
        }
      }

      .message-test {
        h4 {
          margin-bottom: 10px;
        }
      }
    }

    .message-logs {
      .log-controls {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 15px;

        .log-count {
          color: #909399;
          font-size: 14px;
        }
      }

      .logs-container {
        height: 400px;
        overflow-y: auto;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 10px;
        background-color: #fafafa;

        .log-item {
          margin-bottom: 10px;
          padding: 8px;
          border-radius: 4px;
          background-color: white;
          border-left: 3px solid #e4e7ed;

          &.success {
            border-left-color: #67c23a;
          }

          &.error {
            border-left-color: #f56c6c;
          }

          &.send {
            border-left-color: #409eff;
          }

          &.receive {
            border-left-color: #67c23a;
          }

          .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;

            .log-time {
              font-size: 12px;
              color: #909399;
            }
          }

          .log-content {
            pre {
              margin: 0;
              font-family: 'Courier New', monospace;
              font-size: 12px;
              white-space: pre-wrap;
              word-break: break-all;
            }
          }
        }
      }
    }
  }
}
</style>
