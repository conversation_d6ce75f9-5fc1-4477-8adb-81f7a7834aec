package com.xju.codeduel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xju.codeduel.mapper.BattleRecordsMapper;
import com.xju.codeduel.mapper.ProblemsMapper;
import com.xju.codeduel.mapper.ProblemsTagsMapper;
import com.xju.codeduel.mapper.UserBattleRecordMapper;
import com.xju.codeduel.mapper.UserRatingHistoriesMapper;
import com.xju.codeduel.mapper.UsersMapper;
// import com.xju.codeduel.websocket.RoomWebSocketHandler;
import com.xju.codeduel.model.domain.BattleRecords;
import com.xju.codeduel.model.domain.Problems;
import com.xju.codeduel.model.domain.ProblemsTags;
import com.xju.codeduel.model.domain.UserBattleRecord;
import com.xju.codeduel.model.domain.UserRatingHistories;
import com.xju.codeduel.model.domain.Users;
import com.xju.codeduel.model.dto.RoomCreateDTO;
import com.xju.codeduel.model.dto.RoomInfoDTO;
import com.xju.codeduel.model.dto.RoomJoinDTO;

import com.xju.codeduel.service.IRoomService;
import com.xju.codeduel.service.UserRoomService;
import com.xju.codeduel.event.BattleEndEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 房间管理服务实现类
 * 
 * 功能说明：
 * 1. 实现房间的创建、加入、查询等核心业务功能
 * 2. 使用内存缓存管理活跃房间信息，提高性能
 * 3. 结合数据库持久化存储房间和对战记录
 * 
 * 技术实现：
 * - 使用ConcurrentHashMap作为房间缓存，支持并发访问
 * - 事务管理确保数据一致性
 * - 日志记录便于问题排查和监控
 * 
 * 设计考虑：
 * - 内存缓存：活跃房间信息存储在内存中，快速响应
 * - 数据持久化：重要数据同步到数据库，防止数据丢失
 * - 并发安全：使用线程安全的数据结构和同步机制
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class RoomServiceImpl implements IRoomService {

    private static final Logger logger = LoggerFactory.getLogger(RoomServiceImpl.class);

    // ==================== 依赖注入 ====================
    
    @Autowired
    private BattleRecordsMapper battleRecordsMapper;
    
    @Autowired
    private UserBattleRecordMapper userBattleRecordMapper;
    
    @Autowired
    private UsersMapper usersMapper;
    
    @Autowired
    private ProblemsMapper problemsMapper;

    @Autowired
    private ProblemsTagsMapper problemsTagsMapper;

    @Autowired
    private UserRatingHistoriesMapper userRatingHistoriesMapper;

    @Autowired
    private UserRoomService userRoomService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    // @Autowired
    // private RoomWebSocketHandler webSocketHandler;





    // ==================== 内存缓存 ====================
    
    /**
     * 房间信息缓存
     * Key: 房间码 (Long)
     * Value: 房间信息 (RoomInfoDTO)
     * 
     * 使用ConcurrentHashMap确保线程安全
     */
    private final Map<Long, RoomInfoDTO> roomCache = new ConcurrentHashMap<>();
    
    /**
     * 房间码生成器，确保唯一性
     */
    private final Set<Long> usedRoomCodes = ConcurrentHashMap.newKeySet();

    // ==================== 核心业务方法 ====================

    @Override
    @Transactional
    public RoomInfoDTO createRoom(RoomCreateDTO createDTO) {
        logger.info("🏠 开始创建房间，创建者: {}", createDTO);
        
        try {
            // 1. 验证创建者信息
            Users creator = usersMapper.selectById(createDTO.getCreatorId());
            if (creator == null) {
                throw new RuntimeException("创建者用户不存在");
            }
            
            // 2. 选择题目（如果未指定则根据条件筛选）
            Problems problem = selectProblem(createDTO.getProblemId(), createDTO.getMinDifficulty(),
                                           createDTO.getMaxDifficulty(), createDTO.getExcludedTags());
            if (problem == null) {
                throw new RuntimeException("无法获取符合条件的题目");
            }
            
            // 3. 生成唯一房间码
            Long roomCode = generateRoomCode();
            
            // 4. 构建房间信息DTO（暂时简化，不保存到数据库）
            RoomInfoDTO roomInfo = new RoomInfoDTO();
            roomInfo.setRoomId(System.currentTimeMillis()); // 使用时间戳作为临时ID
            roomInfo.setRoomCode(roomCode);
            roomInfo.setStatus(RoomInfoDTO.RoomStatus.WAITING.name());
            roomInfo.setCreator(creator);
            roomInfo.setProblem(problem);
            roomInfo.setDescription(createDTO.getDescription());
            roomInfo.setCreateTime(LocalDateTime.now());
            
            // 初始化参与者列表（只有创建者）
            List<Users> participants = new ArrayList<>();
            participants.add(creator);
            roomInfo.setParticipants(participants);
            roomInfo.setParticipantCount(1);
            roomInfo.setMaxParticipants(2);
            roomInfo.setIsMatchBattle(false); // 默认为房间对战

            // 5. 添加到缓存
            roomCache.put(roomCode, roomInfo);

            // 6. 更新创建者的房间状态
            updateUserRoomStatus(creator.getId(), roomCode, "WAITING");

            logger.info("✅ 房间创建成功，房间码: {}, 创建者: {}", roomCode, createDTO.getCreatorName());
            return roomInfo;
            
        } catch (Exception e) {
            logger.error("❌ 创建房间失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建房间失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RoomInfoDTO joinRoom(RoomJoinDTO joinDTO) {
        logger.info("🚪 用户尝试加入房间，请求: {}", joinDTO);
        
        try {
            // 1. 验证房间是否存在
            RoomInfoDTO roomInfo = roomCache.get(joinDTO.getRoomCode());
            if (roomInfo == null) {
                throw new RuntimeException("房间不存在或已关闭");
            }
            
            // 2. 验证房间状态
            if (!RoomInfoDTO.RoomStatus.WAITING.name().equals(roomInfo.getStatus())) {
                throw new RuntimeException("房间当前状态不允许加入");
            }
            
            // 3. 验证房间是否已满
            if (roomInfo.isFull()) {
                throw new RuntimeException("房间已满");
            }
            
            // 4. 验证用户信息
            Users user = usersMapper.selectById(joinDTO.getUserId());
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 5. 检查用户是否已在房间中
            boolean alreadyInRoom = roomInfo.getParticipants().stream()
                    .anyMatch(p -> p.getId().equals(joinDTO.getUserId()));
            if (alreadyInRoom) {
                throw new RuntimeException("您已在该房间中");
            }
            
            // 6. 添加用户到房间
            roomInfo.getParticipants().add(user);
            
            // 7. 更新参与者数量
            roomInfo.setParticipantCount(roomInfo.getParticipants().size());
            
            // 8. 更新房间状态（如果人数足够）
            String newStatus = "WAITING";
            if (roomInfo.canStart()) {
                roomInfo.setStatus(RoomInfoDTO.RoomStatus.READY.name());
                newStatus = "READY";
                logger.info("🎯 房间 {} 人数已满，状态更新为READY", joinDTO.getRoomCode());

                // 更新所有参与者的状态为READY
                for (Users participant : roomInfo.getParticipants()) {
                    updateUserRoomStatus(participant.getId(), joinDTO.getRoomCode(), "READY");
                }
            } else {
                // 更新新加入用户的状态为WAITING
                updateUserRoomStatus(user.getId(), joinDTO.getRoomCode(), "WAITING");
            }

            // 9. 更新缓存
            roomCache.put(joinDTO.getRoomCode(), roomInfo);

            logger.info("✅ 用户 {} 成功加入房间 {}", user.getCodeforcesId(), joinDTO.getRoomCode());
            return roomInfo;
            
        } catch (Exception e) {
            logger.error("❌ 加入房间失败: {}", e.getMessage(), e);
            throw new RuntimeException("加入房间失败: " + e.getMessage());
        }
    }

    @Override
    public RoomInfoDTO getRoomInfo(Long roomCode) {
        logger.debug("📋 查询房间信息，房间码: {}", roomCode);
        return roomCache.get(roomCode);
    }

    @Override
    @Transactional
    public RoomInfoDTO startBattle(Long roomCode) {
        logger.info("⚔️ 开始对战，房间码: {}", roomCode);
        
        try {
            RoomInfoDTO roomInfo = roomCache.get(roomCode);
            if (roomInfo == null) {
                throw new RuntimeException("房间不存在");
            }
            
            if (!roomInfo.canStart()) {
                throw new RuntimeException("房间人数不足，无法开始对战");
            }
            
            if (!RoomInfoDTO.RoomStatus.READY.name().equals(roomInfo.getStatus())) {
                throw new RuntimeException("房间状态不允许开始对战");
            }
            
            // 更新房间状态和开始时间
            roomInfo.setStatus(RoomInfoDTO.RoomStatus.BATTLING.name());
            roomInfo.setStartTime(LocalDateTime.now());
            
            // 更新数据库记录（暂时简化）
            // TODO: 完善数据库更新逻辑
            
            // 更新缓存
            roomCache.put(roomCode, roomInfo);
            
            logger.info("✅ 房间 {} 对战开始", roomCode);
            return roomInfo;
            
        } catch (Exception e) {
            logger.error("❌ 开始对战失败: {}", e.getMessage(), e);
            throw new RuntimeException("开始对战失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 选择题目
     * 如果指定了题目ID则使用指定题目，否则根据条件筛选后随机选择
     *
     * @param problemId 指定的题目ID
     * @param minDifficulty 最小难度
     * @param maxDifficulty 最大难度
     * @param excludedTags 排除的标签ID列表
     * @return 选择的题目
     */
    private Problems selectProblem(Long problemId, Integer minDifficulty, Integer maxDifficulty, List<Long> excludedTags) {
        if (problemId != null) {
            // 如果指定了题目ID，直接使用
            return problemsMapper.selectById(problemId);
        } else {
            // 根据条件筛选题目
            try {
                // 设置默认值
                if (minDifficulty == null) minDifficulty = 800;
                if (maxDifficulty == null) maxDifficulty = 3500;

                // 构建查询条件
                LambdaQueryWrapper<Problems> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.ge(Problems::getDifficulty, minDifficulty)
                           .le(Problems::getDifficulty, maxDifficulty)
                           .eq(Problems::getIsDeleted, 0);

                List<Problems> problems = problemsMapper.selectList(queryWrapper);

                // 如果有排除的标签，进一步筛选
                if (excludedTags != null && !excludedTags.isEmpty()) {
                    logger.info("🏷️ 开始标签筛选，排除标签: {}", excludedTags);

                    // 查询包含排除标签的题目ID
                    List<Long> excludedProblemIds = problemsTagsMapper.selectList(
                        new QueryWrapper<ProblemsTags>().in("tag_id", excludedTags)
                    ).stream().map(ProblemsTags::getProblemId).distinct().collect(Collectors.toList());

                    if (!excludedProblemIds.isEmpty()) {
                        // 从结果中排除这些题目
                        problems = problems.stream()
                                .filter(problem -> !excludedProblemIds.contains(problem.getId()))
                                .collect(Collectors.toList());

                        logger.info("🏷️ 标签筛选完成，排除了 {} 道题目，剩余 {} 道题目",
                                   excludedProblemIds.size(), problems.size());
                    }
                }

                if (problems.isEmpty()) {
                    logger.warn("⚠️ 没有找到符合条件的题目，难度范围: {}-{}, 排除标签: {}",
                               minDifficulty, maxDifficulty, excludedTags);
                    return null;
                }

                // 随机选择一个题目
                Random random = new Random();
                Problems selectedProblem = problems.get(random.nextInt(problems.size()));

                logger.info("🎯 随机选择题目: {} (难度: {})", selectedProblem.getTitle(), selectedProblem.getDifficulty());
                return selectedProblem;

            } catch (Exception e) {
                logger.error("❌ 选择题目失败: {}", e.getMessage(), e);
                return null;
            }
        }
    }

    @Override
    public Long generateRoomCode() {
        Random random = new Random();
        Long roomCode;
        
        // 生成6位数字的房间码，确保唯一性
        do {
            roomCode = 100000L + random.nextInt(900000); // 生成100000-999999之间的数字
        } while (usedRoomCodes.contains(roomCode) || roomCache.containsKey(roomCode));
        
        usedRoomCodes.add(roomCode);
        return roomCode;
    }

    @Override
    public boolean isValidRoomCode(Long roomCode) {
        return roomCode != null && roomCode >= 100000L && roomCode <= 999999L;
    }

    @Override
    public void updateRoomCache(Long roomCode, RoomInfoDTO roomInfo) {
        logger.debug("🔄 更新房间缓存，房间码: {}", roomCode);
        roomCache.put(roomCode, roomInfo);
    }

    @Override
    public List<RoomInfoDTO> getActiveRooms() {
        return roomCache.values().stream()
                .filter(room -> RoomInfoDTO.RoomStatus.WAITING.name().equals(room.getStatus()) ||
                               RoomInfoDTO.RoomStatus.READY.name().equals(room.getStatus()))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public RoomInfoDTO finishBattle(Long roomCode, Long winnerId, String reason) {
        logger.info("🏁 对战结束，房间码: {}, 获胜者ID: {}, 原因: {}", roomCode, winnerId, reason);

        try {
            // 1. 获取房间信息
            RoomInfoDTO roomInfo = roomCache.get(roomCode);
            if (roomInfo == null) {
                throw new RuntimeException("房间不存在");
            }

            // 2. 验证房间状态
            if (!RoomInfoDTO.RoomStatus.BATTLING.name().equals(roomInfo.getStatus())) {
                throw new RuntimeException("房间当前状态不允许结束对战");
            }

            // 3. 更新房间状态和结束时间
            roomInfo.setStatus(RoomInfoDTO.RoomStatus.FINISHED.name());
            roomInfo.setEndTime(LocalDateTime.now());

            // 3.1 设置对战结果信息
            RoomInfoDTO.BattleResult battleResult = new RoomInfoDTO.BattleResult();
            battleResult.setWinnerId(winnerId);
            battleResult.setReason(reason);
            battleResult.setEndTime(LocalDateTime.now());
            roomInfo.setBattleResult(battleResult);

            // 4. 插入对战记录到数据库
            BattleRecords battleRecord = new BattleRecords();
            battleRecord.setProblemId(roomInfo.getProblem().getId());
            // 根据房间类型设置对战记录类型：0=匹配对战，1=房间对战
            battleRecord.setIsRoom(roomInfo.getIsMatchBattle() != null && roomInfo.getIsMatchBattle() ? 0 : 1);
            battleRecord.setStartTime(roomInfo.getStartTime());
            battleRecord.setEndTime(roomInfo.getEndTime());
            battleRecord.setRoomCode(roomCode);

            // 插入对战记录
            battleRecordsMapper.insert(battleRecord);
            Long battleId = battleRecord.getId();

            logger.info("📝 对战记录已插入，ID: {}", battleId);

            // 5. 插入用户对战记录关联
            List<Users> participants = roomInfo.getParticipants();
            for (Users participant : participants) {
                UserBattleRecord userBattleRecord = new UserBattleRecord();
                userBattleRecord.setUserId(participant.getId());
                userBattleRecord.setBattleId(battleId);
                userBattleRecordMapper.insert(userBattleRecord);
            }

            // 6. 更新用户Rating并记录变化历史
            // 根据房间类型决定是否计算分数：匹配对战计算分数，房间对战不计算分数
            boolean isRoomBattle = !(roomInfo.getIsMatchBattle() != null && roomInfo.getIsMatchBattle());
            updateUserRatings(participants, winnerId, battleId, isRoomBattle);

            logger.info("✅ 对战结果已保存，获胜者: {}, 对战记录ID: {}", winnerId, battleId);

            // 7. 更新缓存
            roomCache.put(roomCode, roomInfo);

            // 8. 通过WebSocket通知房间内所有用户对战结束 (暂时注释掉)
            // RoomWebSocketHandler.RoomUpdateMessage updateMessage = new RoomWebSocketHandler.RoomUpdateMessage();
            // updateMessage.setType("BATTLE_FINISHED");
            // updateMessage.setRoomInfo(roomInfo);
            // updateMessage.setMessage(winnerId != null ? "对战结束，获胜者: " + getWinnerName(participants, winnerId) : "对战结束，平局");
            // updateMessage.setTimestamp(System.currentTimeMillis());
            //
            // webSocketHandler.broadcastRoomUpdate(roomCode, updateMessage);

            // 9. 清除所有参与者的房间状态
            for (Users participant : participants) {
                clearUserRoomStatus(participant.getId());
            }

            // 10. 发布对战结束事件，通知其他服务进行清理
            List<Long> participantIds = participants.stream()
                    .map(Users::getId)
                    .collect(Collectors.toList());

            BattleEndEvent battleEndEvent = new BattleEndEvent(
                    this, participantIds, roomCode, winnerId, reason);
            eventPublisher.publishEvent(battleEndEvent);

            logger.info("📢 已发布对战结束事件，房间: {}", roomCode);

            // 10. 清理房间（可选，也可以保留一段时间供查看结果）
            // 这里暂时保留房间信息，可以后续添加定时清理机制

            logger.info("✅ 房间 {} 对战结束处理完成", roomCode);
            return roomInfo;

        } catch (Exception e) {
            logger.error("❌ 结束对战失败: {}", e.getMessage(), e);
            throw new RuntimeException("结束对战失败: " + e.getMessage());
        }
    }

    @Override
    public RoomInfoDTO leaveRoom(Long roomCode, Long userId) {
        logger.info("🚪 用户离开房间，房间码: {}, 用户ID: {}", roomCode, userId);

        try {
            // 1. 从缓存中获取房间信息
            RoomInfoDTO roomInfo = roomCache.get(roomCode);
            if (roomInfo == null) {
                throw new RuntimeException("房间不存在");
            }

            // 2. 检查用户是否在房间中
            List<Users> participants = roomInfo.getParticipants();
            Users userToRemove = null;
            for (Users participant : participants) {
                if (participant.getId().equals(userId)) {
                    userToRemove = participant;
                    break;
                }
            }

            if (userToRemove == null) {
                throw new RuntimeException("用户不在房间中");
            }

            // 3. 检查是否是房主离开
            boolean isCreator = roomInfo.getCreator().getId().equals(userId);

            // 4. 移除用户
            participants.remove(userToRemove);
            roomInfo.setParticipantCount(participants.size());

            if (isCreator || participants.isEmpty()) {
                // 房主离开或房间空了，解散房间
                logger.info("🏠 房间 {} 解散，原因: {}", roomCode, isCreator ? "房主离开" : "房间为空");

                // 清除所有剩余成员的房间状态
                for (Users participant : participants) {
                    clearUserRoomStatus(participant.getId());
                    logger.info("🧹 清除用户 {} 的房间状态（房间已解散）", participant.getId());
                }

                // 从缓存中移除房间
                roomCache.remove(roomCode);
                usedRoomCodes.remove(roomCode);

                return null; // 房间已解散
            } else {
                // 更新房间状态
                if (participants.size() == 1) {
                    // 只剩一个人，状态改为WAITING
                    roomInfo.setStatus(RoomInfoDTO.RoomStatus.WAITING.name());
                }

                // 更新缓存
                roomCache.put(roomCode, roomInfo);

                logger.info("✅ 用户 {} 离开房间 {}，剩余 {} 人", userId, roomCode, participants.size());

                return roomInfo;
            }

        } catch (Exception e) {
            logger.error("❌ 离开房间失败: {}", e.getMessage(), e);
            throw new RuntimeException("离开房间失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户Rating并记录变化历史
     *
     * @param participants 参与对战的用户列表
     * @param winnerId 获胜者ID（null表示平局）
     * @param battleId 对战记录ID
     * @param isRoomBattle 是否为房间对战（true=房间对战分数不变，false=匹配对战使用ELO算法）
     */
    private void updateUserRatings(List<Users> participants, Long winnerId, Long battleId, boolean isRoomBattle) {
        logger.info("始更新用户Rating，对战类型: {}", isRoomBattle ? "房间对战" : "匹配对战");

        for (Users participant : participants) {
            int oldRating = participant.getRating();
            int newRating;
            String reason;

            if (isRoomBattle) {
                // 房间对战：分数不变
                newRating = oldRating;
                reason = winnerId != null && winnerId.equals(participant.getId()) ? "房间对战胜利" : "房间对战失败";
                logger.info("房间对战 - 用户 {} Rating保持不变: {}", participant.getCodeforcesId(), oldRating);
            } else {
                // 匹配对战：使用ELO算法计算分数变化
                if (winnerId == null) {
                    newRating = oldRating;
                    reason = "匹配对战平局";
                } else if (winnerId.equals(participant.getId())) {
                    // 胜利：根据对手实力计算加分
                    Users opponent = participants.stream()
                        .filter(p -> !p.getId().equals(participant.getId()))
                        .findFirst().orElse(null);

                    if (opponent != null) {
                        newRating = calculateEloRating(oldRating, opponent.getRating(), true);
                        reason = "匹配对战成功";
                    } else {
                        newRating = oldRating + 25; // 默认加分
                        reason = "匹配对战成功";
                    }
                } else {
                    // 失败：根据对手实力计算扣分
                    Users opponent = participants.stream()
                        .filter(p -> !p.getId().equals(winnerId))
                        .findFirst().orElse(null);

                    if (opponent != null) {
                        newRating = calculateEloRating(oldRating, opponent.getRating(), false);
                        reason = "匹配对战失败";
                    } else {
                        newRating = Math.max(800, oldRating - 25); // 默认扣分，最低800
                        reason = "匹配对战失败";
                    }
                }

                // 更新用户表中的Rating
                participant.setRating(newRating);
                usersMapper.updateById(participant);

                logger.info("匹配对战 - 用户 {} Rating变化: {} -> {} ({})",
                    participant.getCodeforcesId(), oldRating, newRating,
                    newRating > oldRating ? "+" + (newRating - oldRating) : String.valueOf(newRating - oldRating));
            }

            // 记录Rating变化历史
            UserRatingHistories ratingHistory = new UserRatingHistories();
            ratingHistory.setUserId(participant.getId());
            ratingHistory.setBattleId(battleId);
            ratingHistory.setOldRating(oldRating);
            ratingHistory.setNewRating(newRating);
            ratingHistory.setReason(reason);
            ratingHistory.setRecordTime(LocalDateTime.now());

            userRatingHistoriesMapper.insert(ratingHistory);

            logger.info("Rating历史记录已保存 - 用户: {}, 变化: {} -> {}, 原因: {}",
                participant.getCodeforcesId(), oldRating, newRating, reason);
        }

        logger.info("所有用户Rating更新完成");
    }

    /**
     * 使用ELO算法计算新的Rating
     *
     * ELO算法说明：
     * 1. K因子：决定Rating变化幅度，这里使用32（标准值）
     * 2. 期望胜率：根据双方Rating差计算
     * 3. 实际结果：胜利=1，失败=0
     * 4. 新Rating = 旧Rating + K * (实际结果 - 期望胜率)
     *
     * @param playerRating 玩家当前Rating
     * @param opponentRating 对手当前Rating
     * @param isWin 是否获胜
     * @return 新的Rating值
     */
    private int calculateEloRating(int playerRating, int opponentRating, boolean isWin) {
        // K因子：Rating变化的最大幅度
        final int K_FACTOR = 32;

        // 计算期望胜率（使用ELO公式）
        double expectedScore = 1.0 / (1.0 + Math.pow(10.0, (opponentRating - playerRating) / 400.0));

        // 实际结果：胜利=1，失败=0
        double actualScore = isWin ? 1.0 : 0.0;

        // 计算新Rating
        double ratingChange = K_FACTOR * (actualScore - expectedScore);
        int newRating = (int) Math.round(playerRating + ratingChange);

        // 限制Rating范围：最低800，最高4000
        newRating = Math.max(800, Math.min(4000, newRating));

        logger.debug("ELO计算 - 玩家Rating: {}, 对手Rating: {}, 期望胜率: {:.2f}, 实际结果: {}, Rating变化: {:.1f}, 新Rating: {}",
            playerRating, opponentRating, expectedScore, actualScore, ratingChange, newRating);

        return newRating;
    }

    /**
     * 获取获胜者姓名
     *
     * @param participants 参与者列表
     * @param winnerId 获胜者ID
     * @return 获胜者姓名
     */
    private String getWinnerName(List<Users> participants, Long winnerId) {
        return participants.stream()
            .filter(user -> user.getId().equals(winnerId))
            .map(Users::getCodeforcesId)
            .findFirst()
            .orElse("未知用户");
    }

    /**
     * 更新用户房间状态
     *
     * @param userId 用户ID
     * @param roomCode 房间号
     * @param status 房间状态
     */
    private void updateUserRoomStatus(Long userId, Long roomCode, String status) {
        logger.info("更新用户 {} 房间状态: 房间={}, 状态={}", userId, roomCode, status);

        try {
            Users user = new Users();
            user.setId(userId);
            user.setCurrentRoomCode(roomCode);
            user.setRoomStatus(status);

            usersMapper.updateById(user);

            logger.info("用户 {} 房间状态已更新", userId);
        } catch (Exception e) {
            logger.error("更新用户 {} 房间状态失败: {}", userId, e.getMessage());
        }
    }

    /**
     * 清除用户房间状态
     *
     * @param userId 用户ID
     */
    private void clearUserRoomStatus(Long userId) {
        logger.info("清除用户 {} 的房间状态", userId);

        try {
            Users user = new Users();
            user.setId(userId);
            user.setCurrentRoomCode(null);
            user.setRoomStatus("NONE");

            usersMapper.updateById(user);

            logger.info("用户 {} 房间状态已清除", userId);
        } catch (Exception e) {
            logger.error("清除用户 {} 房间状态失败: {}", userId, e.getMessage());
        }
    }

}
