package com.xju.codeduel.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.common.utls.PythonServiceUtils;
import com.xju.codeduel.model.domain.Problems;
import com.xju.codeduel.model.domain.Tags;
import com.xju.codeduel.model.domain.ProblemsTags;
import com.xju.codeduel.service.IProblemsService;
import com.xju.codeduel.service.ITagsService;
import com.xju.codeduel.service.IProblemsTagsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 后台管理控制器
 * 
 * 功能说明：
 * 1. 管理题目和标签数据的更新
 * 2. 通过Python服务从Codeforces获取数据
 * 3. 提供数据更新状态查询
 * 
 * API接口：
 * - POST /api/admin/update-problems - 更新题目数据
 * - POST /api/admin/update-tags - 更新标签数据
 * - GET /api/admin/data-status - 获取数据更新状态
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/api/admin")
@Api(tags = "后台管理")
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    private IProblemsService problemsService;

    @Autowired
    private ITagsService tagsService;

    @Autowired
    private IProblemsTagsService problemsTagsService;

    @Autowired
    private PythonServiceUtils pythonServiceUtils;

    /**
     * 从Codeforces更新题目数据
     * 
     * 接口说明：
     * 1. 调用Python服务从Codeforces API获取题目数据
     * 2. 解析并保存到本地数据库
     * 3. 支持配置难度范围和获取页数
     * 
     * @param requestData 更新配置，包含maxPages、minRating、maxRating等
     * @return 更新结果
     */
    @PostMapping("/update-problems")
    @ApiOperation(value = "更新题目数据", notes = "从Codeforces获取最新题目数据并保存到数据库")
    public JsonResponse<Map<String, Object>> updateProblemsFromCodeforces(
            @ApiParam(value = "更新配置", required = true)
            @RequestBody Map<String, Object> requestData) {
        
        Integer maxPages = requestData.get("maxPages") != null ? 
                          Integer.valueOf(requestData.get("maxPages").toString()) : 10;
        Integer minRating = requestData.get("minRating") != null ? 
                           Integer.valueOf(requestData.get("minRating").toString()) : 800;
        Integer maxRating = requestData.get("maxRating") != null ? 
                           Integer.valueOf(requestData.get("maxRating").toString()) : 3500;
        
        logger.info("🔄 开始更新题目数据，配置: maxPages={}, minRating={}, maxRating={}", 
                   maxPages, minRating, maxRating);
        
        try {
            // 检查Python服务是否可用
            if (!pythonServiceUtils.isServiceAvailable()) {
                return JsonResponse.failure("Python服务不可用，请检查服务状态");
            }
            
            // 调用Python服务更新题目数据
            String pythonServiceUrl = "http://localhost:5000/api/codeforces/problems/update";

            // 构建请求数据
            Map<String, Object> pythonRequestData = Map.of(
                "maxPages", maxPages,
                "minRating", minRating,
                "maxRating", maxRating
            );

            // 发送HTTP请求到Python服务
            java.net.http.HttpClient httpClient = java.net.http.HttpClient.newHttpClient();
            java.net.http.HttpRequest httpRequest = java.net.http.HttpRequest.newBuilder()
                    .uri(java.net.URI.create(pythonServiceUrl))
                    .header("Content-Type", "application/json")
                    .POST(java.net.http.HttpRequest.BodyPublishers.ofString(
                        new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(pythonRequestData)))
                    .timeout(java.time.Duration.ofSeconds(60))
                    .build();

            java.net.http.HttpResponse<String> pythonResponse = httpClient.send(httpRequest,
                java.net.http.HttpResponse.BodyHandlers.ofString());

            if (pythonResponse.statusCode() != 200) {
                throw new RuntimeException("Python服务调用失败，状态码: " + pythonResponse.statusCode());
            }

            // 解析Python服务返回的数据
            ObjectMapper objectMapper = new ObjectMapper();
            @SuppressWarnings("unchecked")
            Map<String, Object> pythonResult = objectMapper.readValue(pythonResponse.body(), Map.class);

            if (!(Boolean) pythonResult.get("success")) {
                throw new RuntimeException("Python服务返回错误: " + pythonResult.get("message"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) pythonResult.get("data");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> problems = (List<Map<String, Object>>) data.get("problems");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> problemTagRelations = (List<Map<String, Object>>) data.get("problemTagRelations");

            logger.info("📥 从Python服务获取到 {} 道题目和 {} 个标签关系",
                       problems.size(), problemTagRelations.size());

            // 保存题目数据到数据库
            int savedProblems = saveProblemsToDatabase(problems);

            // 保存标签数据到数据库
            int savedTags = saveTagsToDatabase(problemTagRelations);

            // 保存题目标签关系到数据库
            int savedRelations = saveProblemTagRelationsToDatabase(problemTagRelations);

            // 获取当前题目总数
            long totalCount = problemsService.count();
            
            Map<String, Object> result = Map.of(
                "totalCount", totalCount,
                "updateTime", System.currentTimeMillis(),
                "savedProblems", savedProblems,
                "savedTags", savedTags,
                "savedRelations", savedRelations,
                "config", Map.of(
                    "maxPages", maxPages,
                    "minRating", minRating,
                    "maxRating", maxRating
                )
            );
            
            logger.info("✅ 题目数据更新完成，总数: {}，新增题目: {}，新增标签: {}，新增关系: {}",
                       totalCount, savedProblems, savedTags, savedRelations);
            return JsonResponse.success(result);
            
        } catch (Exception e) {
            logger.error("❌ 更新题目数据失败: {}", e.getMessage(), e);
            return JsonResponse.failure("更新题目数据失败: " + e.getMessage());
        }
    }

    /**
     * 从Codeforces更新标签数据
     * 
     * 接口说明：
     * 1. 调用Python服务从Codeforces API获取标签数据
     * 2. 解析并保存到本地数据库
     * 3. 支持强制更新配置
     * 
     * @param requestData 更新配置，包含forceUpdate等
     * @return 更新结果
     */
    @PostMapping("/update-tags")
    @ApiOperation(value = "更新标签数据", notes = "从Codeforces获取最新标签数据并保存到数据库")
    public JsonResponse<Map<String, Object>> updateTagsFromCodeforces(
            @ApiParam(value = "更新配置", required = true)
            @RequestBody Map<String, Object> requestData) {
        
        Boolean forceUpdate = requestData.get("forceUpdate") != null ? 
                             Boolean.valueOf(requestData.get("forceUpdate").toString()) : false;
        
        logger.info("🔄 开始更新标签数据，强制更新: {}", forceUpdate);
        
        try {
            // 检查Python服务是否可用
            if (!pythonServiceUtils.isServiceAvailable()) {
                return JsonResponse.failure("Python服务不可用，请检查服务状态");
            }
            
            // 调用Python服务更新标签数据
            String pythonServiceUrl = "http://localhost:5000/api/codeforces/tags/update";

            // 构建请求数据
            Map<String, Object> pythonRequestData = Map.of(
                "forceUpdate", forceUpdate
            );

            // 发送HTTP请求到Python服务
            java.net.http.HttpClient httpClient = java.net.http.HttpClient.newHttpClient();
            java.net.http.HttpRequest httpRequest = java.net.http.HttpRequest.newBuilder()
                    .uri(java.net.URI.create(pythonServiceUrl))
                    .header("Content-Type", "application/json")
                    .POST(java.net.http.HttpRequest.BodyPublishers.ofString(
                        new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(pythonRequestData)))
                    .timeout(java.time.Duration.ofSeconds(60))
                    .build();

            java.net.http.HttpResponse<String> pythonResponse = httpClient.send(httpRequest,
                java.net.http.HttpResponse.BodyHandlers.ofString());

            if (pythonResponse.statusCode() != 200) {
                throw new RuntimeException("Python服务调用失败，状态码: " + pythonResponse.statusCode());
            }

            // 解析Python服务返回的数据
            ObjectMapper objectMapper = new ObjectMapper();
            @SuppressWarnings("unchecked")
            Map<String, Object> pythonResult = objectMapper.readValue(pythonResponse.body(), Map.class);

            if (!(Boolean) pythonResult.get("success")) {
                throw new RuntimeException("Python服务返回错误: " + pythonResult.get("message"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) pythonResult.get("data");
            @SuppressWarnings("unchecked")
            List<String> tags = (List<String>) data.get("tags");

            logger.info("📥 从Python服务获取到 {} 个标签", tags.size());

            // 保存标签数据到数据库
            int savedTags = saveTagNamesToDatabase(tags);

            // 获取当前标签总数
            long totalCount = tagsService.count();
            
            Map<String, Object> result = Map.of(
                "totalCount", totalCount,
                "updateTime", System.currentTimeMillis(),
                "savedTags", savedTags,
                "config", Map.of(
                    "forceUpdate", forceUpdate
                )
            );

            logger.info("✅ 标签数据更新完成，总数: {}，新增: {}", totalCount, savedTags);
            return JsonResponse.success(result);
            
        } catch (Exception e) {
            logger.error("❌ 更新标签数据失败: {}", e.getMessage(), e);
            return JsonResponse.failure("更新标签数据失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询题目列表
     *
     * 接口说明：
     * 1. 支持分页查询题目数据
     * 2. 支持按标题搜索
     * 3. 支持按难度范围筛选
     * 4. 返回题目详细信息
     *
     * @param pageNo 页码（从1开始）
     * @param pageSize 每页大小
     * @param title 题目标题（可选，模糊搜索）
     * @param minDifficulty 最小难度（可选）
     * @param maxDifficulty 最大难度（可选）
     * @return 分页题目列表
     */
    @GetMapping("/problems")
    @ApiOperation(value = "分页查询题目", notes = "支持分页、搜索和筛选的题目列表查询")
    public JsonResponse<Map<String, Object>> getProblemsPage(
            @ApiParam(value = "页码", required = true) @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页大小", required = true) @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "题目标题") @RequestParam(required = false) String title,
            @ApiParam(value = "最小难度") @RequestParam(required = false) Integer minDifficulty,
            @ApiParam(value = "最大难度") @RequestParam(required = false) Integer maxDifficulty) {

        logger.info("📋 分页查询题目，页码: {}, 大小: {}, 标题: {}, 难度范围: {}-{}",
                   pageNo, pageSize, title, minDifficulty, maxDifficulty);

        try {
            // 构建查询条件
            LambdaQueryWrapper<Problems> queryWrapper = new LambdaQueryWrapper<>();

            // 标题模糊搜索
            if (title != null && !title.trim().isEmpty()) {
                queryWrapper.like(Problems::getTitle, title.trim());
            }

            // 难度范围筛选
            if (minDifficulty != null) {
                queryWrapper.ge(Problems::getDifficulty, minDifficulty);
            }
            if (maxDifficulty != null) {
                queryWrapper.le(Problems::getDifficulty, maxDifficulty);
            }

            // 排除已删除的题目
            queryWrapper.eq(Problems::getIsDeleted, 0);

            // 按难度升序排列
            queryWrapper.orderByAsc(Problems::getDifficulty);

            // 分页查询
            Page<Problems> page = new Page<>(pageNo, pageSize);
            Page<Problems> resultPage = problemsService.page(page, queryWrapper);

            Map<String, Object> result = Map.of(
                "records", resultPage.getRecords(),
                "total", resultPage.getTotal(),
                "current", resultPage.getCurrent(),
                "size", resultPage.getSize(),
                "pages", resultPage.getPages()
            );

            logger.info("✅ 查询完成，共 {} 条记录，当前第 {} 页", resultPage.getTotal(), pageNo);
            return JsonResponse.success(result);

        } catch (Exception e) {
            logger.error("❌ 查询题目列表失败: {}", e.getMessage(), e);
            return JsonResponse.failure("查询题目列表失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询标签列表
     *
     * 接口说明：
     * 1. 支持分页查询标签数据
     * 2. 支持按标签名搜索
     * 3. 返回标签详细信息
     *
     * @param pageNo 页码（从1开始）
     * @param pageSize 每页大小
     * @param tagName 标签名（可选，模糊搜索）
     * @return 分页标签列表
     */
    @GetMapping("/tags")
    @ApiOperation(value = "分页查询标签", notes = "支持分页和搜索的标签列表查询")
    public JsonResponse<Map<String, Object>> getTagsPage(
            @ApiParam(value = "页码", required = true) @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页大小", required = true) @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "标签名") @RequestParam(required = false) String tagName) {

        logger.info("🏷️ 分页查询标签，页码: {}, 大小: {}, 标签名: {}", pageNo, pageSize, tagName);

        try {
            // 构建查询条件
            LambdaQueryWrapper<Tags> queryWrapper = new LambdaQueryWrapper<>();

            // 标签名模糊搜索
            if (tagName != null && !tagName.trim().isEmpty()) {
                queryWrapper.like(Tags::getTagName, tagName.trim());
            }

            // 按标签名升序排列
            queryWrapper.orderByAsc(Tags::getTagName);

            // 分页查询
            Page<Tags> page = new Page<>(pageNo, pageSize);
            Page<Tags> resultPage = tagsService.page(page, queryWrapper);

            Map<String, Object> result = Map.of(
                "records", resultPage.getRecords(),
                "total", resultPage.getTotal(),
                "current", resultPage.getCurrent(),
                "size", resultPage.getSize(),
                "pages", resultPage.getPages()
            );

            logger.info("✅ 查询完成，共 {} 条记录，当前第 {} 页", resultPage.getTotal(), pageNo);
            return JsonResponse.success(result);

        } catch (Exception e) {
            logger.error("❌ 查询标签列表失败: {}", e.getMessage(), e);
            return JsonResponse.failure("查询标签列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据更新状态
     *
     * 接口说明：
     * 1. 查询题目和标签的数据统计
     * 2. 返回最后更新时间等信息
     * 3. 用于前端显示数据状态
     *
     * @return 数据状态信息
     */
    @GetMapping("/data-status")
    @ApiOperation(value = "获取数据状态", notes = "查询题目和标签的数据统计信息")
    public JsonResponse<Map<String, Object>> getDataUpdateStatus() {

        logger.debug("📊 查询数据更新状态");
        
        try {
            // 获取题目数据统计
            long problemsCount = problemsService.count();
            
            // 获取标签数据统计
            long tagsCount = tagsService.count();
            
            // 构建状态数据
            Map<String, Object> statusData = new HashMap<>();

            Map<String, Object> problemsData = new HashMap<>();
            problemsData.put("totalCount", problemsCount);
            problemsData.put("lastUpdate", "暂无数据"); // TODO: 从配置或数据库获取最后更新时间
            problemsData.put("status", "idle");

            Map<String, Object> tagsData = new HashMap<>();
            tagsData.put("totalCount", tagsCount);
            tagsData.put("lastUpdate", "暂无数据"); // TODO: 从配置或数据库获取最后更新时间
            tagsData.put("status", "idle");

            statusData.put("problems", problemsData);
            statusData.put("tags", tagsData);
            statusData.put("pythonServiceStatus", pythonServiceUtils.isServiceAvailable());
            
            return JsonResponse.success(statusData);
            
        } catch (Exception e) {
            logger.error("❌ 获取数据状态失败: {}", e.getMessage(), e);
            return JsonResponse.failure("获取数据状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查Python服务状态
     * 
     * @return Python服务状态
     */
    @GetMapping("/python-service/status")
    @ApiOperation(value = "检查Python服务状态", notes = "检查Python服务是否可用")
    public JsonResponse<Map<String, Object>> checkPythonServiceStatus() {
        
        try {
            boolean isAvailable = pythonServiceUtils.isServiceAvailable();
            
            Map<String, Object> statusData = Map.of(
                "isAvailable", isAvailable,
                "checkTime", System.currentTimeMillis(),
                "serviceUrl", "http://localhost:5000" // Python服务地址
            );
            
            return JsonResponse.success(statusData);
            
        } catch (Exception e) {
            logger.error("❌ 检查Python服务状态失败: {}", e.getMessage(), e);
            return JsonResponse.failure("检查Python服务状态失败: " + e.getMessage());
        }
    }

    /**
     * 清理缓存
     * 
     * @return 清理结果
     */
    @PostMapping("/clear-cache")
    @ApiOperation(value = "清理缓存", notes = "清理系统缓存数据")
    public JsonResponse<String> clearCache() {
        
        logger.info("🧹 开始清理系统缓存");
        
        try {
            // TODO: 实现缓存清理逻辑
            // 例如：清理房间缓存、用户缓存等
            
            logger.info("✅ 系统缓存清理完成");
            return JsonResponse.success("缓存清理完成");
            
        } catch (Exception e) {
            logger.error("❌ 清理缓存失败: {}", e.getMessage(), e);
            return JsonResponse.failure("清理缓存失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 保存题目数据到数据库
     *
     * 功能说明：
     * 1. 将从Codeforces获取的题目数据保存到problems表
     * 2. 使用批量插入提高性能
     * 3. 避免重复插入相同的题目
     *
     * @param problems 题目数据列表
     * @return 实际保存的题目数量
     */
    private int saveProblemsToDatabase(List<Map<String, Object>> problems) {
        logger.info("💾 开始保存 {} 道题目到数据库", problems.size());

        int savedCount = 0;
        LocalDateTime now = LocalDateTime.now();

        for (Map<String, Object> problemData : problems) {
            try {
                // 构建题目唯一标识
                Integer contestId = (Integer) problemData.get("contestId");
                String index = (String) problemData.get("index");
                String problemId = contestId + index;

                // 检查题目是否已存在
                LambdaQueryWrapper<Problems> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Problems::getContestId, contestId)
                           .eq(Problems::getProblemId, index);

                Problems existingProblem = problemsService.getOne(queryWrapper);

                if (existingProblem == null) {
                    // 创建新题目
                    Problems problem = new Problems();
                    problem.setTitle((String) problemData.get("name"));
                    problem.setDifficulty((Integer) problemData.get("rating"));
                    problem.setContestId(contestId);
                    problem.setProblemId(index);
                    problem.setCreatedTime(now);
                    problem.setUpdateTime(now);
                    problem.setIsDeleted(0);

                    problemsService.save(problem);
                    savedCount++;

                    logger.debug("✅ 保存新题目: {} ({}{})", problem.getTitle(), contestId, index);
                } else {
                    // 更新现有题目
                    existingProblem.setTitle((String) problemData.get("name"));
                    existingProblem.setDifficulty((Integer) problemData.get("rating"));
                    existingProblem.setUpdateTime(now);

                    problemsService.updateById(existingProblem);

                    logger.debug("🔄 更新题目: {} ({}{})", existingProblem.getTitle(), contestId, index);
                }

            } catch (Exception e) {
                logger.error("❌ 保存题目失败: {}, 错误: {}", problemData, e.getMessage());
            }
        }

        logger.info("✅ 题目保存完成，新增: {} 道", savedCount);
        return savedCount;
    }

    /**
     * 保存标签数据到数据库
     *
     * 功能说明：
     * 1. 从题目标签关系中提取所有标签
     * 2. 保存到tags表，避免重复
     * 3. 返回保存的标签数量
     *
     * @param problemTagRelations 题目标签关系列表
     * @return 实际保存的标签数量
     */
    private int saveTagsToDatabase(List<Map<String, Object>> problemTagRelations) {
        // 提取所有唯一的标签名
        Set<String> uniqueTagNames = problemTagRelations.stream()
                .map(relation -> (String) relation.get("tagName"))
                .collect(Collectors.toSet());

        logger.info("💾 开始保存 {} 个标签到数据库", uniqueTagNames.size());

        int savedCount = 0;
        LocalDateTime now = LocalDateTime.now();

        for (String tagName : uniqueTagNames) {
            try {
                // 检查标签是否已存在
                LambdaQueryWrapper<Tags> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Tags::getTagName, tagName);

                Tags existingTag = tagsService.getOne(queryWrapper);

                if (existingTag == null) {
                    // 创建新标签
                    Tags tag = new Tags();
                    tag.setTagName(tagName);
                    tag.setCreatedTime(now);

                    tagsService.save(tag);
                    savedCount++;

                    logger.debug("✅ 保存新标签: {}", tagName);
                }

            } catch (Exception e) {
                logger.error("❌ 保存标签失败: {}, 错误: {}", tagName, e.getMessage());
            }
        }

        logger.info("✅ 标签保存完成，新增: {} 个", savedCount);
        return savedCount;
    }

    /**
     * 保存标签字符串列表到数据库
     *
     * 功能说明：
     * 1. 将标签字符串列表保存到tags表
     * 2. 避免重复插入相同的标签
     * 3. 返回保存的标签数量
     *
     * @param tagNames 标签名列表
     * @return 实际保存的标签数量
     */
    private int saveTagNamesToDatabase(List<String> tagNames) {
        logger.info("💾 开始保存 {} 个标签到数据库", tagNames.size());

        int savedCount = 0;
        LocalDateTime now = LocalDateTime.now();

        for (String tagName : tagNames) {
            try {
                // 检查标签是否已存在
                LambdaQueryWrapper<Tags> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Tags::getTagName, tagName);

                Tags existingTag = tagsService.getOne(queryWrapper);

                if (existingTag == null) {
                    // 创建新标签
                    Tags tag = new Tags();
                    tag.setTagName(tagName);
                    tag.setCreatedTime(now);

                    tagsService.save(tag);
                    savedCount++;

                    logger.debug("✅ 保存新标签: {}", tagName);
                }

            } catch (Exception e) {
                logger.error("❌ 保存标签失败: {}, 错误: {}", tagName, e.getMessage());
            }
        }

        logger.info("✅ 标签保存完成，新增: {} 个", savedCount);
        return savedCount;
    }

    /**
     * 保存题目标签关系到数据库
     *
     * 功能说明：
     * 1. 建立题目和标签的多对多关系
     * 2. 保存到problems_tags表
     * 3. 避免重复关系
     *
     * @param problemTagRelations 题目标签关系列表
     * @return 实际保存的关系数量
     */
    private int saveProblemTagRelationsToDatabase(List<Map<String, Object>> problemTagRelations) {
        logger.info("💾 开始保存 {} 个题目标签关系到数据库", problemTagRelations.size());

        int savedCount = 0;

        for (Map<String, Object> relation : problemTagRelations) {
            try {
                Integer contestId = (Integer) relation.get("contestId");
                String problemIndex = (String) relation.get("problemIndex");
                String tagName = (String) relation.get("tagName");

                // 查找题目ID
                LambdaQueryWrapper<Problems> problemQuery = new LambdaQueryWrapper<>();
                problemQuery.eq(Problems::getContestId, contestId)
                           .eq(Problems::getProblemId, problemIndex);
                Problems problem = problemsService.getOne(problemQuery);

                if (problem == null) {
                    logger.warn("⚠️ 未找到题目: {}_{}", contestId, problemIndex);
                    continue;
                }

                // 查找标签ID
                LambdaQueryWrapper<Tags> tagQuery = new LambdaQueryWrapper<>();
                tagQuery.eq(Tags::getTagName, tagName);
                Tags tag = tagsService.getOne(tagQuery);

                if (tag == null) {
                    logger.warn("⚠️ 未找到标签: {}", tagName);
                    continue;
                }

                // 检查关系是否已存在
                LambdaQueryWrapper<ProblemsTags> relationQuery = new LambdaQueryWrapper<>();
                relationQuery.eq(ProblemsTags::getProblemId, problem.getId())
                            .eq(ProblemsTags::getTagId, tag.getId());

                ProblemsTags existingRelation = problemsTagsService.getOne(relationQuery);

                if (existingRelation == null) {
                    // 创建新关系
                    ProblemsTags problemTag = new ProblemsTags();
                    problemTag.setProblemId(problem.getId());
                    problemTag.setTagId(tag.getId());

                    problemsTagsService.save(problemTag);
                    savedCount++;

                    logger.debug("✅ 保存题目标签关系: {} - {}", problem.getTitle(), tagName);
                }

            } catch (Exception e) {
                logger.error("❌ 保存题目标签关系失败: {}, 错误: {}", relation, e.getMessage());
            }
        }

        logger.info("✅ 题目标签关系保存完成，新增: {} 个", savedCount);
        return savedCount;
    }
}
