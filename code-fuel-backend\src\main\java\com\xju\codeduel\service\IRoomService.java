package com.xju.codeduel.service;

import com.xju.codeduel.model.dto.RoomCreateDTO;
import com.xju.codeduel.model.dto.RoomInfoDTO;
import com.xju.codeduel.model.dto.RoomJoinDTO;

import java.util.List;

/**
 * 房间管理服务接口
 * 
 * 功能说明：
 * 1. 提供房间的创建、加入、查询等核心业务功能
 * 2. 管理房间状态和参与者信息
 * 3. 支持房间的生命周期管理
 * 
 * 设计原则：
 * - 单一职责：专门负责房间相关的业务逻辑
 * - 接口隔离：提供清晰的方法定义
 * - 依赖倒置：面向接口编程，便于测试和扩展
 * 
 * 业务流程：
 * 1. 用户创建房间 -> createRoom()
 * 2. 其他用户加入房间 -> joinRoom()
 * 3. 查询房间信息 -> getRoomInfo()
 * 4. 开始对战 -> startBattle()
 * 5. 结束对战 -> finishBattle()
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface IRoomService {

    /**
     * 创建房间
     * 
     * 功能说明：
     * 1. 根据创建请求生成新的房间
     * 2. 生成唯一的房间码
     * 3. 初始化房间状态为WAITING
     * 4. 将创建者添加为第一个参与者
     * 
     * @param createDTO 创建房间请求数据
     * @return 创建成功的房间信息
     * @throws RuntimeException 当创建失败时抛出异常
     */
    RoomInfoDTO createRoom(RoomCreateDTO createDTO);

    /**
     * 加入房间
     * 
     * 功能说明：
     * 1. 根据房间码查找对应房间
     * 2. 验证房间是否存在且可加入
     * 3. 将用户添加到房间参与者列表
     * 4. 更新房间状态（如果人数足够则变为READY）
     * 
     * @param joinDTO 加入房间请求数据
     * @return 更新后的房间信息
     * @throws RuntimeException 当房间不存在、已满或其他错误时抛出异常
     */
    RoomInfoDTO joinRoom(RoomJoinDTO joinDTO);

    /**
     * 获取房间信息
     * 
     * 功能说明：
     * 1. 根据房间码查询房间详细信息
     * 2. 包含参与者列表、题目信息、状态等
     * 3. 用于前端显示和状态同步
     * 
     * @param roomCode 房间码
     * @return 房间详细信息，如果房间不存在则返回null
     */
    RoomInfoDTO getRoomInfo(Long roomCode);

    /**
     * 开始对战
     * 
     * 功能说明：
     * 1. 验证房间状态是否可以开始对战
     * 2. 更新房间状态为BATTLING
     * 3. 记录对战开始时间
     * 4. 初始化对战相关数据
     * 
     * @param roomCode 房间码
     * @return 更新后的房间信息
     * @throws RuntimeException 当房间状态不允许开始对战时抛出异常
     */
    RoomInfoDTO startBattle(Long roomCode);

    /**
     * 结束对战
     *
     * 功能说明：
     * 1. 更新房间状态为FINISHED
     * 2. 记录对战结束时间
     * 3. 保存对战结果和Rating变化
     * 4. 清理房间资源
     *
     * @param roomCode 房间码
     * @param winnerId 获胜者用户ID（可为null表示平局）
     * @param reason 结束原因（SOLVED-解题获胜, SURRENDER-投降, QUIT-退出等）
     * @return 更新后的房间信息
     * @throws RuntimeException 当房间状态不正确时抛出异常
     */
    RoomInfoDTO finishBattle(Long roomCode, Long winnerId, String reason);

    /**
     * 离开房间
     * 
     * 功能说明：
     * 1. 将用户从房间参与者列表中移除
     * 2. 更新房间状态
     * 3. 如果是创建者离开且房间未开始，则解散房间
     * 4. 如果对战中离开，则判定为失败
     * 
     * @param roomCode 房间码
     * @param userId 离开的用户ID
     * @return 更新后的房间信息，如果房间被解散则返回null
     */
    RoomInfoDTO leaveRoom(Long roomCode, Long userId);

    /**
     * 获取活跃房间列表
     * 
     * 功能说明：
     * 1. 查询当前所有活跃的房间（WAITING和READY状态）
     * 2. 用于房间列表展示
     * 3. 支持分页查询
     * 
     * @return 活跃房间列表
     */
    List<RoomInfoDTO> getActiveRooms();

    /**
     * 生成唯一房间码
     * 
     * 功能说明：
     * 1. 生成6位数字的房间码
     * 2. 确保房间码的唯一性
     * 3. 避免容易混淆的数字组合
     * 
     * @return 唯一的房间码
     */
    Long generateRoomCode();

    /**
     * 验证房间码是否有效
     *
     * @param roomCode 房间码
     * @return true-有效，false-无效
     */
    boolean isValidRoomCode(Long roomCode);

    /**
     * 更新房间缓存
     *
     * 功能说明：
     * 1. 更新指定房间的缓存信息
     * 2. 用于外部服务修改房间信息后同步缓存
     *
     * @param roomCode 房间码
     * @param roomInfo 更新后的房间信息
     */
    void updateRoomCache(Long roomCode, RoomInfoDTO roomInfo);
}
