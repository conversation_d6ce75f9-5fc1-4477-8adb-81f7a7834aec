package com.xju.codeduel.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 重新加入房间请求
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@ApiModel(value = "重新加入房间请求", description = "用户重新加入房间的请求参数")
public class RejoinRoomRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房间号", required = true)
    private Long roomCode;
}
