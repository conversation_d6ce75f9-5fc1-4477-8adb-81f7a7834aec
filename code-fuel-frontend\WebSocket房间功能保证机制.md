# WebSocket 如何保证房间各个功能的实现

## 概述

WebSocket 在 CodeDuel 项目中通过实时双向通信，确保房间内所有用户的状态同步，保证各个功能的正确实现。本文档详细说明 WebSocket 如何保证房间功能的可靠性和一致性。

## 核心保证机制

### 1. 实时状态同步

#### 问题：HTTP轮询的局限性
- **延迟问题**: 3-5秒的轮询间隔导致状态更新延迟
- **资源浪费**: 频繁的HTTP请求消耗服务器资源
- **状态不一致**: 不同用户可能看到不同的房间状态
- **用户体验差**: 操作反馈不及时

#### WebSocket解决方案
```javascript
// 实时状态同步流程
用户A操作 → 后端处理 → WebSocket广播 → 所有用户立即收到更新

// 示例：用户加入房间
1. 用户B点击"加入房间"
2. 后端验证并更新房间状态
3. WebSocket立即广播给房间内所有用户：
   {
     type: 'USER_JOINED',
     roomInfo: { /* 最新房间信息 */ },
     message: '用户B已加入房间',
     timestamp: 1642567890123
   }
4. 所有用户界面立即更新，显示用户B
```

### 2. 消息可靠性保证

#### 消息确认机制
```javascript
// 后端消息发送确认
@MessageMapping("/room/{roomCode}/join")
@SendTo("/topic/room/{roomCode}")
public RoomUpdateMessage handleJoinRoom(@DestinationVariable Long roomCode, Map<String, Object> message) {
    try {
        // 处理加入逻辑
        RoomInfo updatedRoom = roomService.joinRoom(roomCode, userId);
        
        // 返回确认消息
        return RoomUpdateMessage.builder()
            .type("USER_JOINED")
            .roomInfo(updatedRoom)
            .message(userName + "已加入房间")
            .timestamp(System.currentTimeMillis())
            .build();
            
    } catch (Exception e) {
        // 返回错误消息
        return RoomUpdateMessage.builder()
            .type("ERROR_MESSAGE")
            .message("加入房间失败: " + e.getMessage())
            .timestamp(System.currentTimeMillis())
            .build();
    }
}
```

#### 前端消息去重
```javascript
// 防止重复消息处理
const handleWebSocketMessage = (message) => {
  const messageId = `${message.type}_${message.timestamp}_${message.roomInfo?.roomCode}`
  
  if (messageDeduplicator.isDuplicate(messageId)) {
    console.log('🔄 忽略重复消息:', messageId)
    return
  }
  
  // 处理消息
  processMessage(message)
}
```

### 3. 连接断开处理

#### 自动重连机制
```javascript
// WebSocket断开时的处理流程
onDisconnect: () => {
  console.log('🔌 WebSocket连接断开')
  this.connected = false
  
  // 1. 立即启动HTTP轮询作为备用
  this.startHttpPolling()
  
  // 2. 尝试自动重连
  this.scheduleReconnect()
  
  // 3. 通知用户连接状态
  ElMessage.warning('实时通信连接中断，正在重连...')
}

// 重连成功后恢复订阅
onReconnect: () => {
  // 重新订阅所有房间
  this.resubscribeAllRooms()
  
  // 停止HTTP轮询
  this.stopHttpPolling()
  
  // 同步最新状态
  this.syncLatestState()
}
```

#### 降级方案
```javascript
// WebSocket + HTTP轮询混合模式
const useRoomSync = (roomCode) => {
  const wsConnected = ref(false)
  const pollingActive = ref(false)

  // WebSocket优先，HTTP轮询备用
  const syncRoomState = () => {
    if (wsConnected.value) {
      // 使用WebSocket实时同步
      return webSocketService.subscribeToRoom(roomCode, handleMessage)
    } else {
      // 使用HTTP轮询
      return startHttpPolling()
    }
  }

  return { syncRoomState }
}
```

## 房间功能保证详解

### 1. 房间创建和加入

#### 创建房间保证
```javascript
// 创建房间的完整流程
const createRoom = async (roomData) => {
  try {
    // 1. HTTP API创建房间
    const response = await createRoomAPI(roomData)
    
    if (response.status) {
      // 2. 立即连接WebSocket
      await webSocketService.connect()
      
      // 3. 订阅房间消息
      webSocketService.subscribeToRoom(response.data.roomCode, handleRoomMessage)
      
      // 4. 发送加入消息（房主自动加入）
      webSocketService.sendJoinRoom(response.data.roomCode, currentUser.value)
      
      // 5. 跳转到房间页面
      router.push(`/dashboard/battle/room/${response.data.roomCode}`)
    }
    
  } catch (error) {
    // 创建失败处理
    ElMessage.error('创建房间失败: ' + error.message)
  }
}
```

#### 加入房间保证
```javascript
// 加入房间的状态同步
const joinRoom = async (roomCode) => {
  try {
    // 1. HTTP API验证和加入
    const response = await joinRoomAPI({ roomCode, userId, userName })
    
    if (response.status) {
      // 2. WebSocket通知所有用户
      webSocketService.sendJoinRoom(roomCode, currentUser.value)
      
      // 3. 所有用户立即收到更新
      // 房主界面：参与者列表 +1
      // 新用户界面：显示房间信息
      // 其他用户界面：参与者列表更新
    }
    
  } catch (error) {
    // 加入失败，WebSocket发送错误消息
    webSocketService.sendMessage('/app/room/error', {
      roomCode,
      error: error.message
    })
  }
}
```

### 2. 房间状态管理

#### 状态转换保证
```javascript
// 房间状态自动转换
const checkRoomStatus = (roomInfo) => {
  const { participantCount, maxParticipants, status } = roomInfo
  
  // WAITING → READY 自动转换
  if (status === 'WAITING' && participantCount >= maxParticipants) {
    updateRoomStatus(roomInfo.roomCode, 'READY')
    
    // WebSocket广播状态变化
    webSocketService.broadcastToRoom(roomInfo.roomCode, {
      type: 'ROOM_STATUS_CHANGED',
      roomInfo: { ...roomInfo, status: 'READY' },
      message: '房间已准备就绪，可以开始对战！'
    })
  }
  
  // READY → WAITING 自动转换（有人离开）
  if (status === 'READY' && participantCount < maxParticipants) {
    updateRoomStatus(roomInfo.roomCode, 'WAITING')
    
    webSocketService.broadcastToRoom(roomInfo.roomCode, {
      type: 'ROOM_STATUS_CHANGED',
      roomInfo: { ...roomInfo, status: 'WAITING' },
      message: '等待更多玩家加入...'
    })
  }
}
```

#### 状态一致性检查
```javascript
// 定期状态一致性检查
const validateRoomState = async (roomCode) => {
  try {
    // 1. 从数据库获取权威状态
    const dbRoomInfo = await getRoomFromDatabase(roomCode)
    
    // 2. 与WebSocket缓存状态比较
    const wsRoomInfo = webSocketService.getRoomCache(roomCode)
    
    // 3. 如果不一致，强制同步
    if (!isRoomStateEqual(dbRoomInfo, wsRoomInfo)) {
      console.warn('⚠️ 房间状态不一致，强制同步')
      
      webSocketService.broadcastToRoom(roomCode, {
        type: 'ROOM_STATE_SYNC',
        roomInfo: dbRoomInfo,
        message: '房间状态已同步'
      })
    }
    
  } catch (error) {
    console.error('❌ 房间状态验证失败:', error)
  }
}

// 每30秒执行一次状态检查
setInterval(() => {
  activeRooms.forEach(roomCode => {
    validateRoomState(roomCode)
  })
}, 30000)
```

### 3. 对战开始保证

#### 对战开始同步
```javascript
// 确保所有用户同时开始对战
const startBattle = async (roomCode) => {
  try {
    // 1. 验证房主权限
    if (!isRoomCreator(roomCode, currentUser.value.id)) {
      throw new Error('只有房主可以开始对战')
    }
    
    // 2. 验证房间状态
    if (roomStatus !== 'READY') {
      throw new Error('房间状态不允许开始对战')
    }
    
    // 3. 后端处理对战开始
    const battleInfo = await battleService.startBattle(roomCode)
    
    // 4. WebSocket同步广播
    webSocketService.broadcastToRoom(roomCode, {
      type: 'BATTLE_STARTED',
      roomInfo: battleInfo,
      message: '对战开始！',
      timestamp: Date.now()
    })
    
    // 5. 所有用户立即跳转到对战界面
    // 前端收到消息后自动切换界面
    
  } catch (error) {
    // 开始失败，通知所有用户
    webSocketService.broadcastToRoom(roomCode, {
      type: 'ERROR_MESSAGE',
      message: `开始对战失败: ${error.message}`
    })
  }
}
```

#### 对战状态同步
```javascript
// 对战过程中的状态同步
const syncBattleProgress = (roomCode, userId, progress) => {
  // 实时同步对战进度
  webSocketService.broadcastToRoom(roomCode, {
    type: 'BATTLE_PROGRESS',
    data: {
      userId,
      progress,
      timestamp: Date.now()
    }
  })
}

// 对战结束同步
const finishBattle = async (roomCode, winnerId, reason) => {
  try {
    // 1. 后端处理对战结果
    const result = await battleService.finishBattle(roomCode, winnerId, reason)
    
    // 2. WebSocket广播结果
    webSocketService.broadcastToRoom(roomCode, {
      type: 'BATTLE_ENDED',
      roomInfo: result.roomInfo,
      battleResult: result.battleResult,
      message: `对战结束！获胜者：${result.winner.name}`
    })
    
    // 3. 所有用户看到结果页面
    
  } catch (error) {
    console.error('❌ 对战结束处理失败:', error)
  }
}
```

### 4. 用户离开处理

#### 主动离开
```javascript
const leaveRoom = async (roomCode, userId) => {
  try {
    // 1. HTTP API处理离开
    const response = await leaveRoomAPI({ roomCode, userId })
    
    // 2. WebSocket通知其他用户
    webSocketService.sendLeaveRoom(roomCode, currentUser.value)
    
    // 3. 取消WebSocket订阅
    webSocketService.unsubscribeFromRoom(roomCode)
    
    // 4. 如果是房主离开，解散房间
    if (isRoomCreator(roomCode, userId)) {
      webSocketService.broadcastToRoom(roomCode, {
        type: 'ROOM_DISSOLVED',
        message: '房主已离开，房间解散'
      })
    }
    
  } catch (error) {
    console.error('❌ 离开房间失败:', error)
  }
}
```

#### 异常断开检测
```javascript
// 后端检测用户异常断开
@EventListener
public void handleWebSocketDisconnect(SessionDisconnectEvent event) {
    String sessionId = event.getSessionId();
    Long userId = sessionUserMap.get(sessionId);
    
    if (userId != null) {
        // 查找用户所在房间
        RoomInfo room = roomService.findUserCurrentRoom(userId);
        
        if (room != null) {
            // 自动离开房间
            roomService.leaveRoom(room.getRoomCode(), userId);
            
            // 通知其他用户
            messagingTemplate.convertAndSend("/topic/room/" + room.getRoomCode(),
                RoomUpdateMessage.builder()
                    .type("USER_LEFT")
                    .roomInfo(room)
                    .message("用户异常断开连接")
                    .build());
        }
    }
}
```

## 可靠性保证措施

### 1. 消息幂等性
```javascript
// 确保重复消息不会造成状态错误
const processMessage = (message) => {
  // 消息去重
  if (isDuplicateMessage(message.id)) {
    return
  }
  
  // 状态版本检查
  if (message.version <= currentStateVersion) {
    console.log('忽略过期消息')
    return
  }
  
  // 处理消息
  updateRoomState(message.roomInfo)
  currentStateVersion = message.version
}
```

### 2. 状态回滚机制
```javascript
// 操作失败时的状态回滚
const handleOperationFailure = (operation, previousState) => {
  // 回滚到之前状态
  localRoomInfo.value = previousState
  
  // 通知用户操作失败
  ElMessage.error(`操作失败: ${operation}`)
  
  // 重新同步状态
  refreshRoomInfo()
}
```

### 3. 心跳检测
```javascript
// WebSocket心跳检测
const heartbeatInterval = setInterval(() => {
  if (webSocketService.isConnected()) {
    webSocketService.sendMessage('/app/heartbeat', {
      userId: currentUser.value.id,
      timestamp: Date.now()
    })
  }
}, 30000) // 30秒心跳
```

## 总结

WebSocket 通过以下机制保证房间功能的可靠实现：

### 1. **实时性保证**
- 毫秒级消息传递
- 立即状态同步
- 无延迟用户体验

### 2. **一致性保证**
- 消息广播机制
- 状态版本控制
- 定期一致性检查

### 3. **可靠性保证**
- 自动重连机制
- 消息去重处理
- HTTP轮询降级

### 4. **完整性保证**
- 操作确认机制
- 错误处理和回滚
- 异常断开检测

通过这些机制，WebSocket 确保了房间内所有用户看到一致的状态，操作能够实时同步，异常情况能够正确处理，从而保证了房间功能的正确实现。
