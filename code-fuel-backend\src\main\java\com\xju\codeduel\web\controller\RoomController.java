package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.model.dto.RoomCreateDTO;
import com.xju.codeduel.model.dto.RoomInfoDTO;
import com.xju.codeduel.model.dto.RoomJoinDTO;
import com.xju.codeduel.service.IRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 房间管理控制器
 * 
 * 功能说明：
 * 1. 提供房间相关的REST API接口
 * 2. 处理前端的房间操作请求
 * 3. 统一的异常处理和响应格式
 * 
 * API设计原则：
 * - RESTful风格：使用标准的HTTP方法和状态码
 * - 统一响应格式：使用JsonResponse包装所有响应
 * - 详细的API文档：使用Swagger注解提供接口文档
 * - 完善的日志记录：记录关键操作和异常信息
 * 
 * 接口列表：
 * - POST /api/room/create - 创建房间
 * - POST /api/room/join - 加入房间
 * - GET /api/room/{roomCode} - 获取房间信息
 * - POST /api/room/{roomCode}/start - 开始对战
 * - GET /api/room/active - 获取活跃房间列表
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/api/room")
@Api(tags = "房间管理", description = "房间创建、加入、查询等功能")
public class RoomController {

    private static final Logger logger = LoggerFactory.getLogger(RoomController.class);

    @Autowired
    private IRoomService roomService;

    /**
     * 创建房间
     * 
     * 接口说明：
     * 1. 用户提供创建信息（创建者ID、题目ID等）
     * 2. 系统生成唯一房间码
     * 3. 初始化房间状态为等待中
     * 4. 返回房间详细信息
     * 
     * @param createDTO 创建房间请求数据
     * @return 创建成功的房间信息
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建房间", notes = "用户创建新的对战房间")
    public JsonResponse<RoomInfoDTO> createRoom(
            @ApiParam(value = "创建房间请求数据", required = true)
            @RequestBody RoomCreateDTO createDTO) {
        
        logger.info("🏠 收到创建房间请求: {}", createDTO);
        
        try {
            // 参数验证
            if (createDTO.getCreatorId() == null) {
                return JsonResponse.failure("创建者ID不能为空");
            }
            
            if (createDTO.getCreatorName() == null || createDTO.getCreatorName().trim().isEmpty()) {
                return JsonResponse.failure("创建者用户名不能为空");
            }
            
            // 调用服务层创建房间
            RoomInfoDTO roomInfo = roomService.createRoom(createDTO);
            
            logger.info("✅ 房间创建成功，房间码: {}", roomInfo.getRoomCode());
            return JsonResponse.success(roomInfo);
            
        } catch (Exception e) {
            logger.error("❌ 创建房间失败: {}", e.getMessage(), e);
            return JsonResponse.failure("创建房间失败: " + e.getMessage());
        }
    }

    /**
     * 加入房间
     * 
     * 接口说明：
     * 1. 用户提供房间码和个人信息
     * 2. 系统验证房间是否存在且可加入
     * 3. 将用户添加到房间参与者列表
     * 4. 返回更新后的房间信息
     * 
     * @param joinDTO 加入房间请求数据
     * @return 更新后的房间信息
     */
    @PostMapping("/join")
    @ApiOperation(value = "加入房间", notes = "用户通过房间码加入现有房间")
    public JsonResponse<RoomInfoDTO> joinRoom(
            @ApiParam(value = "加入房间请求数据", required = true)
            @RequestBody RoomJoinDTO joinDTO) {
        
        logger.info("🚪 收到加入房间请求: {}", joinDTO);
        
        try {
            // 参数验证
            if (joinDTO.getRoomCode() == null) {
                return JsonResponse.failure("房间码不能为空");
            }
            
            if (!roomService.isValidRoomCode(joinDTO.getRoomCode())) {
                return JsonResponse.failure("房间码格式不正确");
            }
            
            if (joinDTO.getUserId() == null) {
                return JsonResponse.failure("用户ID不能为空");
            }
            
            if (joinDTO.getUserName() == null || joinDTO.getUserName().trim().isEmpty()) {
                return JsonResponse.failure("用户名不能为空");
            }
            
            // 调用服务层加入房间
            RoomInfoDTO roomInfo = roomService.joinRoom(joinDTO);
            
            logger.info("✅ 用户 {} 成功加入房间 {}", joinDTO.getUserName(), joinDTO.getRoomCode());
            return JsonResponse.success(roomInfo);
            
        } catch (Exception e) {
            logger.error("❌ 加入房间失败: {}", e.getMessage(), e);
            return JsonResponse.failure("加入房间失败: " + e.getMessage());
        }
    }

    /**
     * 离开房间
     *
     * 接口说明：
     * 1. 用户主动离开房间
     * 2. 如果是房主离开且房间未开始，则解散房间
     * 3. 如果是普通成员离开，则从房间中移除
     * 4. 更新房间状态和人数
     *
     * @param requestData 请求数据，包含roomCode和userId
     * @return 操作结果，如果房间解散则返回null
     */
    @PostMapping("/leave")
    @ApiOperation(value = "离开房间", notes = "用户离开房间")
    public JsonResponse<RoomInfoDTO> leaveRoom(
            @ApiParam(value = "离开房间请求数据", required = true)
            @RequestBody Map<String, Object> requestData) {

        Long roomCode = Long.valueOf(requestData.get("roomCode").toString());
        Long userId = Long.valueOf(requestData.get("userId").toString());

        logger.info("收到离开房间请求，房间码: {}, 用户ID: {}", roomCode, userId);

        try {
            RoomInfoDTO roomInfo = roomService.leaveRoom(roomCode, userId);

            if (roomInfo != null) {
                logger.info("用户 {} 成功离开房间 {}", userId, roomCode);
                return JsonResponse.success(roomInfo);
            } else {
                logger.info("房间 {} 已解散", roomCode);
                return JsonResponse.success(null, "房间已解散");
            }

        } catch (Exception e) {
            logger.error("离开房间失败: {}", e.getMessage(), e);
            return JsonResponse.failure("离开房间失败: " + e.getMessage());
        }
    }

    /**
     * 获取房间信息
     * 
     * 接口说明：
     * 1. 根据房间码查询房间详细信息
     * 2. 包含参与者列表、题目信息、状态等
     * 3. 用于前端显示和状态同步
     * 
     * @param roomCode 房间码
     * @return 房间详细信息
     */
    @GetMapping("/{roomCode}")
    @ApiOperation(value = "获取房间信息", notes = "根据房间码查询房间详细信息")
    public JsonResponse<RoomInfoDTO> getRoomInfo(
            @ApiParam(value = "房间码", required = true, example = "123456")
            @PathVariable Long roomCode) {
        
        logger.debug("查询房间信息，房间码: {}", roomCode);
        
        try {
            // 参数验证
            if (!roomService.isValidRoomCode(roomCode)) {
                return JsonResponse.failure("房间码格式不正确");
            }
            
            // 查询房间信息
            RoomInfoDTO roomInfo = roomService.getRoomInfo(roomCode);
            
            if (roomInfo == null) {
                return JsonResponse.failure("房间不存在或已关闭");
            }
            
            return JsonResponse.success(roomInfo);
            
        } catch (Exception e) {
            logger.error("查询房间信息失败: {}", e.getMessage(), e);
            return JsonResponse.failure("查询房间信息失败: " + e.getMessage());
        }
    }

    /**
     * 开始对战
     * 
     * 接口说明：
     * 1. 验证房间状态是否可以开始对战
     * 2. 更新房间状态为对战中
     * 3. 记录对战开始时间
     * 4. 返回更新后的房间信息
     * 
     * @param roomCode 房间码
     * @return 更新后的房间信息
     */
    @PostMapping("/{roomCode}/start")
    @ApiOperation(value = "开始对战", notes = "开始房间内的对战")
    public JsonResponse<RoomInfoDTO> startBattle(
            @ApiParam(value = "房间码", required = true, example = "123456")
            @PathVariable Long roomCode) {
        
        logger.info("收到开始对战请求，房间码: {}", roomCode);
        
        try {
            // 参数验证
            if (!roomService.isValidRoomCode(roomCode)) {
                return JsonResponse.failure("房间码格式不正确");
            }
            
            // 开始对战
            RoomInfoDTO roomInfo = roomService.startBattle(roomCode);
            
            logger.info("房间 {} 对战开始", roomCode);
            return JsonResponse.success(roomInfo);
            
        } catch (Exception e) {
            logger.error("开始对战失败: {}", e.getMessage(), e);
            return JsonResponse.failure("开始对战失败: " + e.getMessage());
        }
    }

    /**
     * 获取活跃房间列表
     * 
     * 接口说明：
     * 1. 查询当前所有活跃的房间（等待中和准备就绪状态）
     * 2. 用于房间列表展示
     * 3. 帮助用户发现可加入的房间
     * 
     * @return 活跃房间列表
     */
    @GetMapping("/active")
    @ApiOperation(value = "获取活跃房间列表", notes = "查询当前所有可加入的房间")
    public JsonResponse<List<RoomInfoDTO>> getActiveRooms() {
        
        logger.debug("查询活跃房间列表");
        
        try {
            List<RoomInfoDTO> activeRooms = roomService.getActiveRooms();
            
            logger.debug("查询到 {} 个活跃房间", activeRooms.size());
            return JsonResponse.success(activeRooms);
            
        } catch (Exception e) {
            logger.error("查询活跃房间列表失败: {}", e.getMessage(), e);
            return JsonResponse.failure("查询活跃房间列表失败: " + e.getMessage());
        }
    }

    /**
     * 生成房间码
     *
     * 接口说明：
     * 1. 生成唯一的6位数字房间码
     * 2. 用于前端预览或测试
     *
     * @return 生成的房间码
     */
    @GetMapping("/generate-code")
    @ApiOperation(value = "生成房间码", notes = "生成唯一的房间码")
    public JsonResponse<Long> generateRoomCode() {

        try {
            Long roomCode = roomService.generateRoomCode();
            return JsonResponse.success(roomCode);

        } catch (Exception e) {
            logger.error("生成房间码失败: {}", e.getMessage(), e);
            return JsonResponse.failure("生成房间码失败: " + e.getMessage());
        }
    }

    /**
     * 结束对战
     *
     * 接口说明：
     * 1. 结束房间内的对战
     * 2. 记录对战结果和获胜者
     * 3. 更新房间状态为FINISHED
     * 4. 保存对战记录到数据库
     *
     * @param requestData 请求数据，包含roomCode、winnerId、reason等
     * @return 更新后的房间信息
     */
    @PostMapping("/finish-battle")
    @ApiOperation(value = "结束对战", notes = "结束房间内的对战并记录结果")
    public JsonResponse<RoomInfoDTO> finishBattle(
            @ApiParam(value = "结束对战请求数据", required = true)
            @RequestBody Map<String, Object> requestData) {

        logger.info("🏁 结束对战请求: {}", requestData);

        try {
            // 参数验证和提取
            Long roomCode = Long.valueOf(requestData.get("roomCode").toString());
            Long winnerId = requestData.get("winnerId") != null ?
                Long.valueOf(requestData.get("winnerId").toString()) : null;
            String reason = requestData.get("reason") != null ?
                requestData.get("reason").toString() : "UNKNOWN";

            // 调用服务层结束对战
            RoomInfoDTO roomInfo = roomService.finishBattle(roomCode, winnerId, reason);

            logger.info("对战结束成功，房间码: {}, 获胜者: {}", roomCode, winnerId);
            return JsonResponse.success(roomInfo);

        } catch (Exception e) {
            logger.error("结束对战失败: {}", e.getMessage(), e);
            return JsonResponse.failure("结束对战失败: " + e.getMessage());
        }
    }
}
