package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.model.dto.RoomInfoDTO;
import com.xju.codeduel.service.IMatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 匹配控制器
 * 
 * 功能说明：
 * 1. 处理随机匹配相关操作
 * 2. 管理匹配队列
 * 3. 自动创建匹配房间
 * 4. 处理匹配取消
 * 
 * API接口：
 * - POST /api/match/start - 开始匹配
 * - POST /api/match/cancel - 取消匹配
 * - GET /api/match/status/{userId} - 获取匹配状态
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/api/match")
@Api(tags = "匹配管理", description = "随机匹配相关操作")
public class MatchController {

    private static final Logger logger = LoggerFactory.getLogger(MatchController.class);

    @Autowired
    private IMatchService matchService;

    /**
     * 开始匹配
     * 
     * 接口说明：
     * 1. 将用户加入匹配队列
     * 2. 尝试寻找合适的对手
     * 3. 如果找到对手，自动创建房间并开始对战
     * 4. 如果没有找到，等待其他用户加入
     * 
     * @param requestData 匹配请求数据，包含userId、minDifficulty、maxDifficulty等
     * @return 匹配结果，可能是等待状态或直接匹配成功
     */
    @PostMapping("/start")
    @ApiOperation(value = "开始匹配", notes = "将用户加入匹配队列，寻找合适的对手")
    public JsonResponse<Map<String, Object>> startMatch(
            @ApiParam(value = "匹配请求数据", required = true)
            @RequestBody Map<String, Object> requestData) {
        
        logger.info("🎯 收到开始匹配请求: {}", requestData);
        
        try {
            // 参数验证和提取
            Long userId = Long.valueOf(requestData.get("userId").toString());
            Integer minDifficulty = requestData.get("minDifficulty") != null ? 
                Integer.valueOf(requestData.get("minDifficulty").toString()) : 800;
            Integer maxDifficulty = requestData.get("maxDifficulty") != null ? 
                Integer.valueOf(requestData.get("maxDifficulty").toString()) : 2000;
            
            // 调用匹配服务
            Map<String, Object> result = matchService.startMatch(userId, minDifficulty, maxDifficulty);
            
            logger.info("✅ 匹配请求处理完成，用户: {}", userId);
            return JsonResponse.success(result);
            
        } catch (Exception e) {
            logger.error("❌ 开始匹配失败: {}", e.getMessage(), e);
            return JsonResponse.failure("开始匹配失败: " + e.getMessage());
        }
    }

    /**
     * 取消匹配
     * 
     * 接口说明：
     * 1. 将用户从匹配队列中移除
     * 2. 清除匹配相关状态
     * 3. 返回取消结果
     * 
     * @param requestData 取消匹配请求数据，包含userId
     * @return 取消结果
     */
    @PostMapping("/cancel")
    @ApiOperation(value = "取消匹配", notes = "将用户从匹配队列中移除")
    public JsonResponse<String> cancelMatch(
            @ApiParam(value = "取消匹配请求数据", required = true)
            @RequestBody Map<String, Object> requestData) {
        
        logger.info("🚫 收到取消匹配请求: {}", requestData);
        
        try {
            // 参数验证和提取
            Long userId = Long.valueOf(requestData.get("userId").toString());
            
            // 调用匹配服务
            boolean success = matchService.cancelMatch(userId);
            
            if (success) {
                logger.info("✅ 取消匹配成功，用户: {}", userId);
                return JsonResponse.success("取消匹配成功");
            } else {
                logger.warn("⚠️ 用户不在匹配队列中，用户: {}", userId);
                return JsonResponse.success("用户不在匹配队列中");
            }
            
        } catch (Exception e) {
            logger.error("❌ 取消匹配失败: {}", e.getMessage(), e);
            return JsonResponse.failure("取消匹配失败: " + e.getMessage());
        }
    }

    /**
     * 获取匹配状态
     * 
     * 接口说明：
     * 1. 查询用户当前的匹配状态
     * 2. 返回匹配进度、等待时间等信息
     * 3. 如果匹配成功，返回房间信息
     * 
     * @param userId 用户ID
     * @return 匹配状态信息
     */
    @GetMapping("/status/{userId}")
    @ApiOperation(value = "获取匹配状态", notes = "查询用户当前的匹配状态")
    public JsonResponse<Map<String, Object>> getMatchStatus(
            @ApiParam(value = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {
        
        logger.info("📊 查询匹配状态，用户: {}", userId);
        
        try {
            // 调用匹配服务
            Map<String, Object> status = matchService.getMatchStatus(userId);
            
            return JsonResponse.success(status);
            
        } catch (Exception e) {
            logger.error("❌ 查询匹配状态失败: {}", e.getMessage(), e);
            return JsonResponse.failure("查询匹配状态失败: " + e.getMessage());
        }
    }
}
