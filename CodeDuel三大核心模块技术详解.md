# CodeDuel三大核心模块技术详解

## 📋 模块概览

CodeDuel项目的三大核心模块分别是：
1. **用户管理模块** - 负责用户认证、信息管理、权限控制
2. **房间管理模块** - 负责房间创建、加入、状态管理
3. **对战模块** - 负责实时对战、匹配算法、评分系统

## 🔐 一、用户管理模块

### 1.1 模块架构

```
用户管理模块架构
├── 前端层
│   ├── Login.vue - 登录界面
│   ├── Profile.vue - 个人资料
│   └── userInfo.js - 用户状态管理
├── 控制器层
│   ├── UsersController.java - 用户API控制器
│   └── AuthController.java - 认证控制器
├── 服务层
│   ├── IUsersService.java - 用户服务接口
│   ├── UsersServiceImpl.java - 用户服务实现
│   └── IVerificationService.java - 验证服务接口
├── 数据层
│   ├── UsersMapper.java - 用户数据访问
│   └── Users.java - 用户实体类
└── 工具层
    ├── SessionUtils.java - 会话管理
    ├── PasswordUtils.java - 密码加密
    └── PythonServiceUtils.java - CF验证服务
```

### 1.2 核心功能实现

#### 1.2.1 用户注册功能

**技术实现：**
- **前端验证**：Vue3表单验证，实时检查用户输入
- **Codeforces验证**：通过Python微服务调用CF API验证用户身份
- **密码加密**：使用SHA-256算法加盐加密
- **数据存储**：MyBatis-Plus自动化CRUD操作

**关键代码流程：**
```java
// 1. 接收注册请求
@PostMapping("/verify-and-register")
public JsonResponse<Users> verifyAndRegister(@RequestBody RegisterDTO registerDTO) {
    
    // 2. 参数验证
    String codeforcesId = registerDTO.getCodeforcesId().trim();
    String password = registerDTO.getPassword().trim();
    String verificationString = registerDTO.getVerificationString().trim();
    
    // 3. 检查用户是否已存在
    Users existingUser = usersService.getUserByUsername(codeforcesId);
    if (existingUser != null) {
        return JsonResponse.failure("该Codeforces用户已注册");
    }
    
    // 4. 调用Python服务验证CF身份
    CodeforcesUserInfo codeforcesUserInfo = verificationService.getCodeforcesUserInfo(codeforcesId);
    boolean isVerified = verificationService.verifyCodeforcesUser(codeforcesId, verificationString);
    
    // 5. 创建用户并保存
    Users newUser = new Users();
    newUser.setCodeforcesId(codeforcesId);
    newUser.setPassword(PasswordUtils.simpleEncrypt(password));
    newUser.setAvatar(codeforcesUserInfo.getAvatar());
    newUser.setRating(codeforcesUserInfo.getRating());
    
    return JsonResponse.success(newUser);
}
```

**涉及技术知识点：**
- **Spring Boot MVC**：RESTful API设计
- **数据验证**：参数校验和业务规则验证
- **密码安全**：SHA-256哈希算法
- **微服务通信**：HTTP客户端调用Python服务
- **事务管理**：Spring事务注解保证数据一致性

#### 1.2.2 用户登录功能

**技术实现：**
- **会话管理**：基于HttpSession的用户会话
- **密码验证**：加密后的密码比对
- **状态持久化**：Pinia状态管理持久化用户信息

**关键代码流程：**
```java
// 后端登录验证
@PostMapping("/login")
public JsonResponse<Users> login(@RequestBody Users users) {
    // 1. 密码加密
    String encryptedPassword = PasswordUtils.simpleEncrypt(users.getPassword());
    users.setPassword(encryptedPassword);
    
    // 2. 数据库验证
    Users loginUser = usersService.login(users);
    
    // 3. 保存会话信息
    if (loginUser != null) {
        SessionUtils.saveCurrentUserInfo(loginUser);
        loginUser.setPassword(null); // 清除密码字段
    }
    
    return JsonResponse.success(loginUser);
}
```

```vue
<!-- 前端登录组件 -->
<script setup>
import { useUserInfoStore } from '@/stores/userInfo'

const userStore = useUserInfoStore()

const handleLogin = async () => {
  try {
    const response = await login(loginForm.value)
    if (response.status) {
      // 保存用户信息到状态管理
      userStore.setUserInfo(response.data)
      ElMessage.success('登录成功')
      router.push('/dashboard')
    }
  } catch (error) {
    ElMessage.error('登录失败')
  }
}
</script>
```

**涉及技术知识点：**
- **会话管理**：HttpSession机制
- **状态管理**：Pinia响应式状态管理
- **路由控制**：Vue Router导航守卫
- **数据持久化**：LocalStorage本地存储

#### 1.2.3 用户信息管理

**技术实现：**
- **实时更新**：用户信息修改后同步更新Session
- **头像上传**：文件上传和存储管理
- **数据同步**：前后端数据一致性保证

**关键技术特点：**
```java
// 用户信息更新时同步Session
@PostMapping("/update")
public JsonResponse update(@RequestBody Users users) {
    boolean update = usersService.updateById(users);
    
    // 如果更新的是当前登录用户，同步更新Session
    Users currentUser = SessionUtils.getCurrentUserInfo();
    if (update && currentUser.getId().equals(users.getId())) {
        Users updatedUser = usersService.getById(users.getId());
        updatedUser.setPassword(null);
        SessionUtils.saveCurrentUserInfo(updatedUser);
    }
    
    return JsonResponse.success(update);
}
```

### 1.3 技术特色

1. **安全性**：密码加密存储，会话管理，权限控制
2. **集成性**：与Codeforces API深度集成
3. **实时性**：用户状态实时同步
4. **扩展性**：支持多种认证方式扩展

## 🏠 二、房间管理模块

### 2.1 模块架构

```
房间管理模块架构
├── 前端层
│   ├── RoomManager.vue - 房间管理界面
│   ├── RoomWaiting.vue - 房间等待界面
│   └── Battle.vue - 对战页面
├── 控制器层
│   ├── RoomController.java - 房间API控制器
│   └── UserRoomController.java - 用户房间关联
├── 服务层
│   ├── IRoomService.java - 房间服务接口
│   ├── RoomServiceImpl.java - 房间服务实现
│   └── UserRoomService.java - 用户房间服务
├── 数据层
│   ├── RoomInfoDTO.java - 房间信息DTO
│   ├── RoomCreateDTO.java - 创建房间DTO
│   └── RoomJoinDTO.java - 加入房间DTO
└── 缓存层
    └── ConcurrentHashMap - 内存缓存房间信息
```

### 2.2 核心功能实现

#### 2.2.1 房间创建功能

**技术实现：**
- **房间码生成**：6位数字唯一房间码算法
- **题目选择**：根据难度和标签筛选题目
- **内存缓存**：ConcurrentHashMap存储房间信息
- **状态管理**：房间状态机管理

**关键代码流程：**
```java
@Override
@Transactional
public RoomInfoDTO createRoom(RoomCreateDTO createDTO) {
    // 1. 验证创建者信息
    Users creator = usersMapper.selectById(createDTO.getCreatorId());
    
    // 2. 选择题目（根据条件筛选）
    Problems problem = selectProblem(
        createDTO.getProblemId(), 
        createDTO.getMinDifficulty(),
        createDTO.getMaxDifficulty(), 
        createDTO.getExcludedTags()
    );
    
    // 3. 生成唯一房间码
    Long roomCode = generateRoomCode();
    
    // 4. 构建房间信息
    RoomInfoDTO roomInfo = new RoomInfoDTO();
    roomInfo.setRoomCode(roomCode);
    roomInfo.setStatus(RoomStatus.WAITING.name());
    roomInfo.setCreator(creator);
    roomInfo.setProblem(problem);
    
    // 5. 添加到缓存
    roomCache.put(roomCode, roomInfo);
    
    return roomInfo;
}

// 房间码生成算法
@Override
public Long generateRoomCode() {
    Random random = new Random();
    Long roomCode;
    
    // 生成6位数字，确保唯一性
    do {
        roomCode = 100000L + random.nextInt(900000);
    } while (usedRoomCodes.contains(roomCode) || roomCache.containsKey(roomCode));
    
    usedRoomCodes.add(roomCode);
    return roomCode;
}
```

**前端房间创建：**
```vue
<script setup>
// 创建房间表单
const createForm = ref({
  problemId: null,
  description: '',
  minDifficulty: 800,
  maxDifficulty: 3500,
  excludedTags: []
})

// 创建房间处理
const handleCreateRoom = async () => {
  try {
    const createData = {
      creatorId: currentUser.value.id,
      creatorName: currentUser.value.codeforcesId,
      problemId: createForm.value.problemId || null,
      description: createForm.value.description || '欢迎来到我的房间',
      minDifficulty: createForm.value.minDifficulty,
      maxDifficulty: createForm.value.maxDifficulty,
      excludedTags: createForm.value.excludedTags
    }

    const response = await createRoom(createData)
    
    if (response.status) {
      ElMessage.success(`房间创建成功！房间码: ${response.data.roomCode}`)
      emit('room-created', response.data)
    }
  } catch (error) {
    ElMessage.error('创建房间失败: ' + error.message)
  }
}
</script>
```

**涉及技术知识点：**
- **算法设计**：唯一ID生成算法
- **缓存技术**：ConcurrentHashMap线程安全缓存
- **数据筛选**：复杂条件的数据库查询
- **状态机**：房间状态转换管理
- **表单验证**：前端数据验证和后端参数校验

#### 2.2.2 房间加入功能

**技术实现：**
- **房间码验证**：格式和存在性验证
- **并发控制**：防止超员加入
- **状态同步**：实时更新房间参与者列表

**关键代码流程：**
```java
@Override
@Transactional
public RoomInfoDTO joinRoom(RoomJoinDTO joinDTO) {
    // 1. 验证房间码
    if (!isValidRoomCode(joinDTO.getRoomCode())) {
        throw new RuntimeException("房间码格式不正确");
    }
    
    // 2. 获取房间信息
    RoomInfoDTO roomInfo = roomCache.get(joinDTO.getRoomCode());
    if (roomInfo == null) {
        throw new RuntimeException("房间不存在或已过期");
    }
    
    // 3. 检查房间状态
    if (!roomInfo.canJoin()) {
        throw new RuntimeException("房间已满或已开始对战");
    }
    
    // 4. 验证用户信息
    Users user = usersMapper.selectById(joinDTO.getUserId());
    if (user == null) {
        throw new RuntimeException("用户不存在");
    }
    
    // 5. 检查是否已在房间中
    boolean alreadyInRoom = roomInfo.getParticipants().stream()
        .anyMatch(p -> p.getId().equals(user.getId()));
    if (alreadyInRoom) {
        throw new RuntimeException("您已在房间中");
    }
    
    // 6. 添加用户到房间
    roomInfo.getParticipants().add(user);
    roomInfo.setParticipantCount(roomInfo.getParticipants().size());
    
    // 7. 检查是否可以开始对战
    if (roomInfo.getParticipantCount() >= roomInfo.getMaxParticipants()) {
        roomInfo.setStatus(RoomStatus.READY.name());
    }
    
    // 8. 更新缓存
    roomCache.put(joinDTO.getRoomCode(), roomInfo);
    
    return roomInfo;
}
```

**涉及技术知识点：**
- **并发控制**：线程安全的状态检查和更新
- **业务规则验证**：复杂的业务逻辑校验
- **数据一致性**：缓存和数据库的一致性保证
- **异常处理**：详细的错误信息和异常处理

#### 2.2.3 房间状态管理

**房间状态机设计：**
```java
public enum RoomStatus {
    WAITING("等待中"),      // 刚创建，等待用户加入
    READY("准备就绪"),      // 人数足够，可以开始对战
    BATTLING("对战中"),     // 正在进行对战
    FINISHED("已结束");     // 对战完成

    private final String description;
    
    // 状态转换规则
    public boolean canTransitionTo(RoomStatus newStatus) {
        switch (this) {
            case WAITING:
                return newStatus == READY;
            case READY:
                return newStatus == BATTLING;
            case BATTLING:
                return newStatus == FINISHED;
            case FINISHED:
                return false; // 结束状态不能转换
            default:
                return false;
        }
    }
}
```

**涉及技术知识点：**
- **状态机模式**：清晰的状态转换逻辑
- **枚举类型**：类型安全的状态定义
- **业务规则**：状态转换的业务约束

### 2.3 技术特色

1. **高性能**：内存缓存提高访问速度
2. **高并发**：线程安全的并发控制
3. **实时性**：房间状态实时同步
4. **可扩展性**：支持多种房间类型扩展

## ⚔️ 三、对战模块

### 3.1 模块架构

```
对战模块架构
├── 前端层
│   ├── BattleRoom.vue - 对战界面
│   ├── websocket.js - WebSocket通信
│   └── Battle.vue - 对战页面容器
├── 控制器层
│   ├── BattleController.java - 对战API控制器
│   ├── MatchController.java - 匹配控制器
│   └── BattleRecordsController.java - 对战记录
├── 服务层
│   ├── IMatchService.java - 匹配服务接口
│   ├── MatchServiceImpl.java - 匹配服务实现
│   ├── IBattleRecordsService.java - 对战记录服务
│   └── IUserRatingHistoriesService.java - 评分历史服务
├── WebSocket层
│   ├── RoomWebSocketHandler.java - 房间WebSocket处理器
│   └── WebSocketConfig.java - WebSocket配置
├── 算法层
│   ├── EloRatingUtils.java - ELO评分算法
│   └── MatchingAlgorithm.java - 匹配算法
└── 事件层
    ├── BattleEndEvent.java - 对战结束事件
    └── BattleEndEventListener.java - 事件监听器
```

### 3.2 核心功能实现

#### 3.2.1 实时对战功能

**技术实现：**
- **WebSocket通信**：双向实时通信
- **状态同步**：对战状态实时同步
- **提交检查**：实时检查用户提交状态
- **倒计时功能**：对战时间限制

**WebSocket配置：**
```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private RoomWebSocketHandler roomWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册WebSocket处理器
        registry.addHandler(roomWebSocketHandler, "/ws/room")
                .setAllowedOrigins("*") // 允许跨域
                .withSockJS(); // 启用SockJS支持
    }
}
```

**WebSocket处理器：**
```java
@Component
public class RoomWebSocketHandler extends TextWebSocketHandler {

    // 存储房间和WebSocket会话的映射
    private final Map<Long, Set<WebSocketSession>> roomSessions = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        logger.info("🔗 WebSocket连接建立: {}", session.getId());

        // 从URL参数获取房间码
        String roomCodeStr = getQueryParam(session, "roomCode");
        if (roomCodeStr != null) {
            Long roomCode = Long.valueOf(roomCodeStr);

            // 将会话添加到对应房间
            roomSessions.computeIfAbsent(roomCode, k -> ConcurrentHashMap.newKeySet())
                       .add(session);

            // 设置会话属性
            session.getAttributes().put("roomCode", roomCode);

            logger.info("✅ 用户加入房间 {} 的WebSocket通信", roomCode);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        logger.debug("📨 收到WebSocket消息: {}", payload);

        try {
            // 解析消息
            Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
            String type = (String) messageData.get("type");
            Long roomCode = (Long) session.getAttributes().get("roomCode");

            // 根据消息类型处理
            switch (type) {
                case "BATTLE_START":
                    handleBattleStart(roomCode, messageData);
                    break;
                case "SUBMISSION_CHECK":
                    handleSubmissionCheck(roomCode, messageData);
                    break;
                case "BATTLE_END":
                    handleBattleEnd(roomCode, messageData);
                    break;
                default:
                    logger.warn("⚠️ 未知的消息类型: {}", type);
            }

        } catch (Exception e) {
            logger.error("❌ 处理WebSocket消息失败: {}", e.getMessage(), e);
        }
    }

    // 广播消息到房间内所有用户
    public void broadcastToRoom(Long roomCode, Object message) {
        Set<WebSocketSession> sessions = roomSessions.get(roomCode);
        if (sessions != null) {
            String messageJson = objectMapper.writeValueAsString(message);
            TextMessage textMessage = new TextMessage(messageJson);

            sessions.removeIf(session -> {
                try {
                    if (session.isOpen()) {
                        session.sendMessage(textMessage);
                        return false;
                    } else {
                        return true; // 移除已关闭的会话
                    }
                } catch (Exception e) {
                    logger.error("❌ 发送WebSocket消息失败: {}", e.getMessage());
                    return true; // 移除出错的会话
                }
            });
        }
    }
}
```

**前端WebSocket通信：**
```vue
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const socket = ref(null)
const battleStatus = ref('waiting')
const timeRemaining = ref(1800) // 30分钟

// 建立WebSocket连接
const connectWebSocket = () => {
  const wsUrl = `ws://localhost:8080/ws/room?roomCode=${roomCode.value}`
  socket.value = new WebSocket(wsUrl)

  socket.value.onopen = () => {
    console.log('🔗 WebSocket连接成功')
  }

  socket.value.onmessage = (event) => {
    const message = JSON.parse(event.data)
    handleWebSocketMessage(message)
  }

  socket.value.onclose = () => {
    console.log('🔌 WebSocket连接关闭')
    // 自动重连
    setTimeout(connectWebSocket, 3000)
  }
}

// 处理WebSocket消息
const handleWebSocketMessage = (message) => {
  switch (message.type) {
    case 'BATTLE_START':
      battleStatus.value = 'battling'
      startTimer()
      break
    case 'SUBMISSION_UPDATE':
      updateSubmissionStatus(message.data)
      break
    case 'BATTLE_END':
      battleStatus.value = 'finished'
      showBattleResult(message.data)
      break
  }
}

// 发送WebSocket消息
const sendWebSocketMessage = (type, data) => {
  if (socket.value && socket.value.readyState === WebSocket.OPEN) {
    const message = { type, data, timestamp: Date.now() }
    socket.value.send(JSON.stringify(message))
  }
}

// 检查提交状态
const checkSubmission = async () => {
  try {
    const response = await checkSubmissionStatus({
      codeforcesHandle: currentUser.value.codeforcesId,
      problemId: problem.value.problemId
    })

    if (response.status && response.data.isPassed) {
      // 通知其他用户
      sendWebSocketMessage('SUBMISSION_SUCCESS', {
        userId: currentUser.value.id,
        username: currentUser.value.codeforcesId
      })

      ElMessage.success('恭喜！您已成功解决题目！')
    }
  } catch (error) {
    ElMessage.error('检查提交状态失败')
  }
}

onMounted(() => {
  connectWebSocket()
})

onUnmounted(() => {
  if (socket.value) {
    socket.value.close()
  }
})
</script>
```

**涉及技术知识点：**
- **WebSocket协议**：全双工通信协议
- **Spring WebSocket**：Spring框架的WebSocket支持
- **SockJS**：WebSocket降级方案
- **并发编程**：ConcurrentHashMap线程安全集合
- **消息序列化**：JSON消息格式
- **自动重连**：网络断线自动重连机制

#### 3.2.2 匹配算法功能

**技术实现：**
- **ELO匹配**：基于用户Rating的智能匹配
- **队列管理**：匹配队列的并发管理
- **超时处理**：匹配超时自动取消
- **房间自动创建**：匹配成功自动创建对战房间

**匹配服务实现：**
```java
@Service
public class MatchServiceImpl implements IMatchService {

    // 匹配队列：按Rating排序
    private final TreeMap<Integer, Queue<MatchRequest>> matchingQueue = new TreeMap<>();
    private final Map<Long, MatchRequest> userMatchRequests = new ConcurrentHashMap<>();

    @Override
    public Map<String, Object> startMatch(Long userId, Integer minDifficulty, Integer maxDifficulty) {
        logger.info("🎯 用户 {} 开始匹配，难度范围: {}-{}", userId, minDifficulty, maxDifficulty);

        // 1. 检查用户是否已在匹配中
        if (userMatchRequests.containsKey(userId)) {
            throw new RuntimeException("您已在匹配队列中，请勿重复匹配");
        }

        // 2. 获取用户信息
        Users user = usersMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 3. 创建匹配请求
        MatchRequest matchRequest = new MatchRequest();
        matchRequest.setUserId(userId);
        matchRequest.setUser(user);
        matchRequest.setUserRating(user.getRating());
        matchRequest.setMinDifficulty(minDifficulty);
        matchRequest.setMaxDifficulty(maxDifficulty);
        matchRequest.setRequestTime(LocalDateTime.now());

        // 4. 尝试寻找匹配对手
        MatchRequest opponent = findOpponent(matchRequest);

        if (opponent != null) {
            // 找到对手，创建对战房间
            return createMatchRoom(matchRequest, opponent);
        } else {
            // 没有找到对手，加入匹配队列
            addToMatchingQueue(matchRequest);
            userMatchRequests.put(userId, matchRequest);

            return Map.of(
                "status", "WAITING",
                "message", "正在寻找对手，请稍候...",
                "estimatedWaitTime", estimateWaitTime(user.getRating())
            );
        }
    }

    // 寻找合适的对手
    private MatchRequest findOpponent(MatchRequest request) {
        int userRating = request.getUserRating();
        int ratingRange = 200; // 初始Rating差距范围

        // 逐步扩大搜索范围
        for (int range = ratingRange; range <= 800; range += 200) {
            int minRating = userRating - range;
            int maxRating = userRating + range;

            // 在Rating范围内搜索
            NavigableMap<Integer, Queue<MatchRequest>> subMap =
                matchingQueue.subMap(minRating, true, maxRating, true);

            for (Queue<MatchRequest> queue : subMap.values()) {
                if (!queue.isEmpty()) {
                    MatchRequest opponent = queue.poll();

                    // 检查难度范围是否兼容
                    if (isDifficultyCompatible(request, opponent)) {
                        userMatchRequests.remove(opponent.getUserId());
                        logger.info("✅ 找到匹配对手：{} vs {}",
                                   request.getUser().getCodeforcesId(),
                                   opponent.getUser().getCodeforcesId());
                        return opponent;
                    } else {
                        // 难度不兼容，重新加入队列
                        queue.offer(opponent);
                    }
                }
            }
        }

        return null; // 没有找到合适的对手
    }

    // 创建匹配房间
    private Map<String, Object> createMatchRoom(MatchRequest player1, MatchRequest player2) {
        try {
            // 1. 选择合适的题目
            Problems problem = selectMatchProblem(player1, player2);

            // 2. 创建房间
            RoomCreateDTO createDTO = new RoomCreateDTO();
            createDTO.setCreatorId(player1.getUserId());
            createDTO.setCreatorName(player1.getUser().getCodeforcesId());
            createDTO.setProblemId(problem.getProblemId());
            createDTO.setDescription("匹配对战房间");

            RoomInfoDTO roomInfo = roomService.createRoom(createDTO);

            // 3. 第二个玩家自动加入房间
            RoomJoinDTO joinDTO = new RoomJoinDTO();
            joinDTO.setRoomCode(roomInfo.getRoomCode());
            joinDTO.setUserId(player2.getUserId());
            joinDTO.setUserName(player2.getUser().getCodeforcesId());

            roomService.joinRoom(joinDTO);

            // 4. 标记为匹配对战
            roomInfo.setIsMatchBattle(true);
            roomService.updateRoomCache(roomInfo.getRoomCode(), roomInfo);

            logger.info("🏠 匹配房间创建成功，房间码: {}", roomInfo.getRoomCode());

            return Map.of(
                "status", "MATCHED",
                "roomCode", roomInfo.getRoomCode(),
                "opponent", player2.getUser(),
                "problem", problem,
                "message", "匹配成功！正在进入对战房间..."
            );

        } catch (Exception e) {
            logger.error("❌ 创建匹配房间失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建匹配房间失败: " + e.getMessage());
        }
    }
}
```

**涉及技术知识点：**
- **数据结构**：TreeMap有序映射，Queue队列
- **并发编程**：ConcurrentHashMap线程安全
- **算法设计**：匹配算法和搜索策略
- **业务逻辑**：复杂的匹配规则实现

#### 3.2.3 ELO评分系统

**技术实现：**
- **ELO算法**：国际象棋评级算法的编程竞赛适配
- **Rating计算**：基于对手实力的动态评分
- **历史记录**：完整的Rating变化历史
- **分数保护**：防止异常的分数波动

**ELO算法核心实现：**
```java
/**
 * ELO评分算法实现
 *
 * 算法原理：
 * 1. 期望胜率 = 1 / (1 + 10^((对手Rating - 自己Rating) / 400))
 * 2. Rating变化 = K因子 × (实际结果 - 期望胜率)
 * 3. 新Rating = 旧Rating + Rating变化
 */
private int calculateEloRating(int playerRating, int opponentRating, boolean isWin) {
    // K因子：决定Rating变化幅度
    final int K_FACTOR = 32;

    // 计算期望胜率（ELO公式）
    double expectedScore = 1.0 / (1.0 + Math.pow(10.0, (opponentRating - playerRating) / 400.0));

    // 实际结果：胜利=1，失败=0
    double actualScore = isWin ? 1.0 : 0.0;

    // 计算Rating变化
    double ratingChange = K_FACTOR * (actualScore - expectedScore);
    int newRating = (int) Math.round(playerRating + ratingChange);

    // 限制Rating范围：最低800，最高4000
    newRating = Math.max(800, Math.min(4000, newRating));

    logger.debug("🧮 ELO计算 - 玩家Rating: {}, 对手Rating: {}, 期望胜率: {:.2f}, " +
                "实际结果: {}, Rating变化: {:.1f}, 新Rating: {}",
                playerRating, opponentRating, expectedScore, actualScore, ratingChange, newRating);

    return newRating;
}
```

**Rating更新流程：**
```java
@Override
@Transactional
public RoomInfoDTO finishBattle(Long roomCode, Long winnerId, String reason) {
    // 1. 获取房间信息
    RoomInfoDTO roomInfo = roomCache.get(roomCode);
    List<Users> participants = roomInfo.getParticipants();
    boolean isMatchBattle = roomInfo.getIsMatchBattle();

    // 2. 更新每个参与者的Rating
    for (Users participant : participants) {
        int oldRating = participant.getRating();
        int newRating;
        String ratingReason;

        if (isMatchBattle) {
            // 匹配对战：使用ELO算法计算分数变化
            if (winnerId == null) {
                // 平局：Rating不变
                newRating = oldRating;
                ratingReason = "匹配对战平局";
            } else if (winnerId.equals(participant.getId())) {
                // 胜利：根据对手实力计算加分
                Users opponent = participants.stream()
                    .filter(p -> !p.getId().equals(participant.getId()))
                    .findFirst().orElse(null);

                if (opponent != null) {
                    newRating = calculateEloRating(oldRating, opponent.getRating(), true);
                    ratingReason = "匹配对战胜利";
                } else {
                    newRating = oldRating + 25; // 默认加分
                    ratingReason = "匹配对战胜利";
                }
            } else {
                // 失败：根据对手实力计算减分
                Users opponent = participants.stream()
                    .filter(p -> !p.getId().equals(participant.getId()))
                    .findFirst().orElse(null);

                if (opponent != null) {
                    newRating = calculateEloRating(oldRating, opponent.getRating(), false);
                    ratingReason = "匹配对战失败";
                } else {
                    newRating = oldRating - 25; // 默认减分
                    ratingReason = "匹配对战失败";
                }
            }
        } else {
            // 房间对战：分数不变
            newRating = oldRating;
            ratingReason = winnerId != null && winnerId.equals(participant.getId()) ? "房间对战胜利" : "房间对战失败";
        }

        // 3. 更新用户Rating
        participant.setRating(newRating);
        usersMapper.updateById(participant);

        // 4. 记录Rating变化历史
        if (newRating != oldRating) {
            UserRatingHistories ratingHistory = new UserRatingHistories();
            ratingHistory.setUserId(participant.getId());
            ratingHistory.setOldRating(oldRating);
            ratingHistory.setNewRating(newRating);
            ratingHistory.setRatingChange(newRating - oldRating);
            ratingHistory.setReason(ratingReason);
            ratingHistory.setChangeTime(LocalDateTime.now());

            userRatingHistoriesMapper.insert(ratingHistory);

            logger.info("📊 用户 {} Rating更新: {} -> {} ({}{})",
                       participant.getCodeforcesId(), oldRating, newRating,
                       newRating > oldRating ? "+" : "", newRating - oldRating);
        }
    }

    // 5. 保存对战记录
    saveBattleRecord(roomInfo, winnerId, reason);

    // 6. 更新房间状态
    roomInfo.setStatus(RoomInfoDTO.RoomStatus.FINISHED.name());
    roomInfo.setEndTime(LocalDateTime.now());
    roomCache.put(roomCode, roomInfo);

    return roomInfo;
}
```

**Rating历史查询：**
```java
@GetMapping("/rating-history/{username}")
public JsonResponse<List<UserRatingHistories>> getRatingHistory(@PathVariable String username) {
    try {
        // 1. 获取用户信息
        Users user = usersService.getUserByUsername(username);
        if (user == null) {
            return JsonResponse.failure("用户不存在");
        }

        // 2. 查询Rating历史
        LambdaQueryWrapper<UserRatingHistories> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserRatingHistories::getUserId, user.getId())
                   .orderByDesc(UserRatingHistories::getChangeTime)
                   .last("LIMIT 50"); // 最近50条记录

        List<UserRatingHistories> ratingHistory = userRatingHistoriesMapper.selectList(queryWrapper);

        return JsonResponse.success(ratingHistory);

    } catch (Exception e) {
        logger.error("❌ 获取Rating历史失败: {}", e.getMessage(), e);
        return JsonResponse.failure("获取Rating历史失败: " + e.getMessage());
    }
}
```

**涉及技术知识点：**
- **数学算法**：ELO评分算法的数学原理
- **概率统计**：期望胜率的概率计算
- **数据库事务**：Rating更新的事务一致性
- **历史记录**：完整的数据变更追踪
- **业务规则**：不同对战模式的评分策略

#### 3.2.4 提交状态检查

**技术实现：**
- **Python服务调用**：通过HTTP调用Python微服务
- **Codeforces API**：查询用户提交记录
- **实时检查**：定时检查提交状态
- **结果缓存**：避免重复查询

**提交检查实现：**
```java
@PostMapping("/check-submission")
public JsonResponse<Map<String, Object>> checkSubmission(
        @RequestParam String codeforcesHandle,
        @RequestParam String problemId) {

    try {
        // 1. 参数验证
        if (codeforcesHandle == null || codeforcesHandle.trim().isEmpty()) {
            return JsonResponse.failure("Codeforces用户名不能为空");
        }

        if (problemId == null || problemId.trim().isEmpty()) {
            return JsonResponse.failure("题目ID不能为空");
        }

        // 2. 检查Python服务是否可用
        if (!pythonServiceUtils.isServiceAvailable()) {
            return JsonResponse.failure("Python服务暂时不可用，请稍后重试");
        }

        // 3. 调用Python服务检查提交状态
        boolean isPassed = pythonServiceUtils.checkSubmissionStatus(codeforcesHandle, problemId);

        // 4. 构建响应数据
        Map<String, Object> responseData = Map.of(
            "isPassed", isPassed,
            "codeforcesHandle", codeforcesHandle,
            "problemId", problemId,
            "checkTime", System.currentTimeMillis()
        );

        logger.info("✅ 提交状态检查完成，用户: {}, 题目: {}, 结果: {}",
                   codeforcesHandle, problemId, isPassed ? "通过" : "未通过");

        return JsonResponse.success(responseData);

    } catch (Exception e) {
        logger.error("❌ 检查提交状态失败: {}", e.getMessage(), e);
        return JsonResponse.failure("检查提交状态失败: " + e.getMessage());
    }
}
```

**Python服务工具类：**
```java
@Component
public class PythonServiceUtils {

    private static final String PYTHON_SERVICE_URL = "http://localhost:5000";
    private static final int TIMEOUT = 10000; // 10秒超时

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 检查用户提交状态
     */
    public boolean checkSubmissionStatus(String codeforcesHandle, String problemId) {
        try {
            String url = PYTHON_SERVICE_URL + "/api/codeforces/check-submission";

            Map<String, String> requestData = Map.of(
                "handle", codeforcesHandle,
                "problemId", problemId
            );

            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestData, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                return Boolean.TRUE.equals(responseBody.get("isPassed"));
            }

            return false;

        } catch (Exception e) {
            logger.error("❌ 调用Python服务检查提交状态失败: {}", e.getMessage());
            throw new RuntimeException("检查提交状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查Python服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            String url = PYTHON_SERVICE_URL + "/api/codeforces/health";
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            logger.warn("⚠️ Python服务不可用: {}", e.getMessage());
            return false;
        }
    }
}
```

**涉及技术知识点：**
- **微服务通信**：HTTP客户端调用
- **服务健康检查**：服务可用性检测
- **异常处理**：网络异常和超时处理
- **数据缓存**：避免重复API调用

### 3.3 技术特色

1. **实时性**：WebSocket双向实时通信
2. **智能性**：ELO算法智能匹配和评分
3. **可靠性**：完善的异常处理和重连机制
4. **扩展性**：支持多种对战模式和评分策略
5. **性能优化**：缓存机制和异步处理

## 🎯 技术总结

### 核心技术栈应用

#### 后端技术
1. **Spring Boot**：微服务架构、依赖注入、自动配置
2. **MyBatis-Plus**：ORM映射、代码生成、分页查询
3. **WebSocket**：实时通信、消息广播、连接管理
4. **并发编程**：线程安全、锁机制、异步处理
5. **算法实现**：ELO评分、匹配算法、状态机

#### 前端技术
1. **Vue.js 3**：Composition API、响应式系统、组件化
2. **Element Plus**：UI组件、表单验证、消息提示
3. **WebSocket客户端**：实时通信、自动重连、消息处理
4. **状态管理**：Pinia集中状态、数据持久化
5. **路由管理**：Vue Router、导航守卫、动态路由

#### 架构设计
1. **微服务架构**：服务拆分、独立部署、技术选型
2. **前后端分离**：API设计、跨域处理、数据交互
3. **缓存策略**：内存缓存、数据缓存、性能优化
4. **事件驱动**：事件发布订阅、异步处理
5. **数据一致性**：事务管理、并发控制、状态同步

### 技术亮点

1. **创新性**：实时编程对战的技术实现
2. **完整性**：从用户管理到对战评分的完整闭环
3. **可扩展性**：模块化设计支持功能扩展
4. **性能优化**：多级缓存和异步处理
5. **用户体验**：实时反馈和流畅交互

这三大核心模块展示了现代Web应用开发的完整技术栈，从前端交互到后端业务逻辑，从实时通信到算法实现，体现了软件工程的最佳实践。
