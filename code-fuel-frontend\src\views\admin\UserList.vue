<script setup>
import { pageUsers, updateUser } from "../../api/api";
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { useUserInfoStore } from "@/stores/userInfo";
import {
  Plus,
  Search,
  Refresh,
  Edit,
  Key,
  UserFilled,
  Lock,
  Unlock,
  Calendar,
  Clock
} from "@element-plus/icons-vue";


// 用户信息存储
const userInfoStore = useUserInfoStore();

// 响应式数据
const users = ref([]);
const total = ref(0);
const username = ref('');
const isAdmin = ref();
const pageNo = ref(1);
const pageSize = ref(10);
const loading = ref(false);

// 对话框相关
const dialogVisible = ref(false);
const passwordDialogVisible = ref(false);
const banDialogVisible = ref(false);
const selectuser = ref({});
const imageUrl = ref('');

// 密码修改相关
const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
});

// 封禁相关
const banForm = ref({
  banReason: ''
});

// 获取用户列表
const getUsers = async () => {
  try {
    loading.value = true;
    const query = {
      codeforcesId: username.value,
      isAdmin: isAdmin.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
    };

    const res = await pageUsers(query);

    if (res.data && Array.isArray(res.data.records)) {
      total.value = res.data.total;
      users.value = res.data.records;
    } else {
      console.error('返回的数据格式不符合要求', res.data);
      users.value = [];
    }
  } catch (error) {
    console.error('获取用户列表失败', error);
    ElMessage.error('获取用户列表失败');
    users.value = [];
  } finally {
    loading.value = false;
  }
};

//页码改变
const pageSizeChange = (val) => {
  console.log(`每页 ${val} 条`);
  pageSize.value = val;

  getUsers();
};

// 页码改变
const pageNoChange = (val) => {
  console.log(`当前页码: ${val}`);
  pageNo.value = val;
  getUsers();
};

// 刷新数据
const handleRefresh = () => {
  username.value = '';
  isAdmin.value = undefined;
  pageNo.value = 1;
  getUsers();
};

// 搜索用户
const handleSearch = () => {
  pageNo.value = 1;
  getUsers();
};

// 显示编辑用户对话框
const showEditDialog = (user) => {
  selectuser.value = JSON.parse(JSON.stringify(user));
  imageUrl.value = selectuser.value.avatar;
  dialogVisible.value = true;
};

// 显示修改密码对话框
const showPasswordDialog = (user) => {
  selectuser.value = JSON.parse(JSON.stringify(user));
  passwordForm.value = {
    newPassword: '',
    confirmPassword: ''
  };
  passwordDialogVisible.value = true;
};

// 修改密码
const submitPassword = async () => {
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致');
    return;
  }

  if (passwordForm.value.newPassword.length < 6) {
    ElMessage.error('密码长度不能少于6位');
    return;
  }

  try {
    const userToUpdate = {
      ...selectuser.value,
      password: passwordForm.value.newPassword
    };

    await updateUser(userToUpdate);
    ElMessage.success('密码修改成功');
    passwordDialogVisible.value = false;
    getUsers();
  } catch (error) {
    ElMessage.error('密码修改失败');
  }
};

// 调用获取用户列表的函数
getUsers();

// 移除未使用的 tableData
// const tableData = [
//   {
//     date: '2016-05-03',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   // ... 其他数据
// ];


// 关闭对话框前的确认操作
// const handleClose = (done) => {
//   ElMessageBox.confirm('你确定关闭对话框吗?')
//       .then(() => {
//         done();
//       })
//       .catch(() => {
//         // 捕获错误，不做额外处理
//       });
// };


const handleAvatarSuccess = (
    response,
    uploadFile
) => {
  selectuser.value.avatar = response.data.url
  imageUrl.value = URL.createObjectURL(uploadFile.raw)
}

const beforeAvatarUpload = (rawFile) => {
  if (rawFile.type !== 'image/jpeg') {
    ElMessage.error('Avatar picture must be JPG format!');
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('Avatar picture size can not exceed 2MB!');
    return false;
  }
  return true;
};

// 修改用户基本信息
const submit = async () => {
  try {
    // 只更新允许修改的字段，避免意外修改密码
    const userToUpdate = {
      id: selectuser.value.id,
      codeforcesId: selectuser.value.codeforcesId,
      avatar: selectuser.value.avatar,
      rating: selectuser.value.rating,
      isAdmin: selectuser.value.isAdmin,
      registerTime: selectuser.value.registerTime,
      lastLogin: selectuser.value.lastLogin,
      status: selectuser.value.status,
      banReason: selectuser.value.banReason
      // 注意：不包含password字段，避免意外修改密码
    };

    await updateUser(userToUpdate);
    ElMessage.success('修改成功');
    dialogVisible.value = false;
    getUsers();
  } catch (error) {
    ElMessage.error('修改失败');
  }
};

// 获取权限显示文本
const getAdminText = (isAdmin) => {
  if (isAdmin === 2) return '超级管理员';
  if (isAdmin === 1) return '管理员';
  return '普通用户';
};

// 获取权限标签类型
const getAdminTagType = (isAdmin) => {
  if (isAdmin === 2) return 'danger';
  if (isAdmin === 1) return 'warning';
  return 'info';
};

/**
 * 检查当前用户是否为超级管理员
 *
 * 权限等级说明：
 * - isAdmin = 0: 普通用户
 * - isAdmin = 1: 管理员
 * - isAdmin = 2: 超级管理员（唯一，只有他能修改其他用户的权限）
 *
 * @returns {boolean} 是否为超级管理员
 */
const isSuperAdmin = () => {
  return userInfoStore.userInfo.isAdmin === 2;
};

// 获取Rating标签类型
const getRatingTagType = (rating) => {
  if (!rating) return 'info';
  if (rating >= 2400) return 'danger';
  if (rating >= 2100) return 'warning';
  if (rating >= 1900) return 'success';
  return 'info';
};

// 获取表格行类名
const getRowClassName = ({ row }) => {
  if (row.status === 1) return 'banned-row';
  if (row.isAdmin === 1) return 'admin-row';
  return '';
};

// 获取用户状态文本
const getStatusText = (status) => {
  return status === 1 ? '已封禁' : '正常';
};

// 获取用户状态标签类型
const getStatusTagType = (status) => {
  return status === 1 ? 'danger' : 'success';
};

// 显示封禁/解封对话框
const showBanDialog = (user) => {
  selectuser.value = JSON.parse(JSON.stringify(user));
  banForm.value = {
    banReason: user.banReason || ''
  };
  banDialogVisible.value = true;
};

// 提交封禁/解封操作
const submitBanAction = async () => {
  try {
    const isCurrentlyBanned = selectuser.value.status === 1;

    if (!isCurrentlyBanned && !banForm.value.banReason.trim()) {
      ElMessage.error('请填写封禁原因');
      return;
    }

    // 只更新必要的字段，避免意外修改密码等敏感信息
    const userToUpdate = {
      id: selectuser.value.id,
      codeforcesId: selectuser.value.codeforcesId,
      avatar: selectuser.value.avatar,
      rating: selectuser.value.rating,
      isAdmin: selectuser.value.isAdmin,
      registerTime: selectuser.value.registerTime,
      lastLogin: selectuser.value.lastLogin,
      status: isCurrentlyBanned ? 0 : 1,
      banReason: isCurrentlyBanned ? null : banForm.value.banReason.trim()
      // 注意：不包含password字段，避免意外修改密码
    };

    await updateUser(userToUpdate);
    ElMessage.success(isCurrentlyBanned ? '用户解封成功' : '用户封禁成功');
    banDialogVisible.value = false;
    getUsers();
  } catch (error) {
    ElMessage.error('操作失败');
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).replace(/\//g, '-');
};

// 格式化时间
const formatTime = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取登录状态样式类
const getLoginStatusClass = (lastLogin) => {
  if (!lastLogin) return 'never-login';

  const now = new Date();
  const loginDate = new Date(lastLogin);
  const diffDays = Math.floor((now - loginDate) / (1000 * 60 * 60 * 24));

  if (diffDays <= 1) return 'recent-login';
  if (diffDays <= 7) return 'week-login';
  if (diffDays <= 30) return 'month-login';
  return 'old-login';
};





</script>

<template>
  <div class="user-list-container">
    <!-- 页面标题和操作区 -->
    <el-card class="header-card">
      <div class="header-content">
        <h2>
          <el-icon><UserFilled /></el-icon>
          用户管理
        </h2>
        <div class="header-actions">
          <el-button type="success" @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 搜索筛选区 -->
    <el-card class="filter-card">
      <el-form :inline="true" class="filter-form">
        <el-form-item label="用户名">
          <el-input
              v-model="username"
              placeholder="搜索用户名..."
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
              @clear="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="权限">
          <el-select
              v-model="isAdmin"
              placeholder="选择权限"
              clearable
              style="width: 150px"
          >
            <el-option label="全部" :value="undefined" />
            <el-option label="超级管理员" :value="2" />
            <el-option label="管理员" :value="1" />
            <el-option label="普通用户" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>


    <!-- 用户列表表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>用户列表 (共 {{ total }} 人)</span>
        </div>
      </template>

      <el-table
          :data="users"
          style="width: 100%"
          v-loading="loading"
          :row-class-name="getRowClassName"
      >
        <el-table-column prop="codeforcesId" label="用户名" width="150" align="center">
          <template #default="{ row }">
            <div class="user-cell">
              <el-avatar :src="row.avatar" :size="40" />
              <div class="user-info">
                <div class="username">{{ row.codeforcesId }}</div>
                <div class="user-id">ID: {{ row.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="rating" label="Rating" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getRatingTagType(row.rating)" size="large">
              {{ row.rating || 1500 }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="large">
              <!-- <el-icon v-if="row.status === 1"><Lock /></el-icon>
              <el-icon v-else><Unlock /></el-icon> -->
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="isAdmin" label="权限" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getAdminTagType(row.isAdmin)" size="large">
              <!-- <el-icon v-if="row.isAdmin === 1"><Setting /></el-icon> -->
              {{ getAdminText(row.isAdmin) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="registerTime" label="注册时间" width="180" align="center">
          <template #default="{ row }">
            <div class="time-cell register-time">
              <div class="time-icon">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="time-content">
                <div class="time-value">
                  {{ row.registerTime ? formatDate(row.registerTime) : '-' }}
                </div>
                <div class="time-detail" v-if="row.registerTime">
                  {{ formatTime(row.registerTime) }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="lastLogin" label="最后登录" width="180" align="center">
          <template #default="{ row }">
            <div class="time-cell login-time">
              <div class="time-icon" :class="getLoginStatusClass(row.lastLogin)">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="time-content">
                <div class="time-value" v-if="row.lastLogin">
                  {{ formatDate(row.lastLogin) }}
                </div>
                <div class="time-value no-login" v-else>
                  从未登录
                </div>
                <div class="time-detail" v-if="row.lastLogin">
                  {{ formatTime(row.lastLogin) }}
                </div>
                <div class="time-detail inactive" v-else>
                  账户未激活
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="320" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" @click="showEditDialog(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="warning" @click="showPasswordDialog(row)">
                <el-icon><Key /></el-icon>
                改密
              </el-button>
              <el-button
                  :type="row.status === 1 ? 'success' : 'danger'"
                  @click="showBanDialog(row)"
              >
                <el-icon v-if="row.status === 1"><Unlock /></el-icon>
                <el-icon v-else><Lock /></el-icon>
                {{ row.status === 1 ? '解封' : '封禁' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pageNo"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 30, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="pageSizeChange"
            @current-change="pageNoChange"
        />
      </div>
    </el-card>

    <!-- 编辑用户对话框 -->
    <el-dialog
        v-model="dialogVisible"
        title="编辑用户信息"
        width="600px"
    >
      <el-form :model="selectuser" label-width="100px">
        <el-form-item label="用户名">
          <el-input
              v-model="selectuser.codeforcesId"
              disabled
              placeholder="用户名不可修改"
          >
            <template #append>
              <el-tooltip content="用户名是唯一标识，不允许修改" placement="top">
                <el-icon><Lock /></el-icon>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>



        <el-form-item label="权限">
          <el-radio-group v-model="selectuser.isAdmin" :disabled="!isSuperAdmin()">
            <el-radio :value="0">普通用户</el-radio>
            <el-radio :value="1">管理员</el-radio>
          </el-radio-group>
          <div v-if="!isSuperAdmin()" class="permission-tip">
            <el-text type="info" size="small">只有超级管理员才能修改用户权限</el-text>
          </div>
        </el-form-item>

        <el-form-item label="头像">
          <el-upload
              class="avatar-uploader"
              action="/api/file/upload"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit">
            <el-icon><Edit /></el-icon>
            保存修改
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
        v-model="passwordDialogVisible"
        title="修改密码"
        width="400px"
    >
      <el-form :model="passwordForm" label-width="100px">
        <el-form-item label="用户">
          <el-input :value="selectuser.codeforcesId" disabled />
        </el-form-item>

        <el-form-item label="新密码">
          <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
          />
        </el-form-item>

        <el-form-item label="确认密码">
          <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPassword">
            <el-icon><Key /></el-icon>
            修改密码
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 封禁/解封对话框 -->
    <el-dialog
        v-model="banDialogVisible"
        :title="selectuser.status === 1 ? '解封用户' : '封禁用户'"
        width="500px"
    >
      <div class="ban-dialog-content">
        <div class="user-info-section">
          <el-avatar :src="selectuser.avatar" :size="50" />
          <div class="user-details">
            <h3>{{ selectuser.codeforcesId }}</h3>
            <p>ID: {{ selectuser.id }}</p>
            <el-tag :type="getStatusTagType(selectuser.status)" size="small">
              {{ getStatusText(selectuser.status) }}
            </el-tag>
          </div>
        </div>

        <el-divider />

        <el-form :model="banForm" label-width="100px" v-if="selectuser.status !== 1">
          <el-form-item label="封禁原因" required>
            <el-input
                v-model="banForm.banReason"
                type="textarea"
                :rows="4"
                placeholder="请详细说明封禁原因..."
                maxlength="500"
                show-word-limit
            />
          </el-form-item>

          <el-alert
              title="封禁说明"
              type="warning"
              :closable="false"
              show-icon
          >
            <template #default>
              <p>• 封禁后用户将无法登录系统</p>
              <p>• 请谨慎操作，确保封禁原因充分</p>
              <p>• 封禁原因将记录在系统中</p>
            </template>
          </el-alert>
        </el-form>

        <div v-else class="unban-section">
          <el-alert
              title="当前封禁原因"
              type="error"
              :closable="false"
              show-icon
          >
            <template #default>
              <p>{{ selectuser.banReason || '无封禁原因记录' }}</p>
            </template>
          </el-alert>

          <el-alert
              title="解封说明"
              type="success"
              :closable="false"
              show-icon
              style="margin-top: 15px;"
          >
            <template #default>
              <p>• 解封后用户将恢复正常使用权限</p>
              <p>• 解封操作将清除封禁原因记录</p>
            </template>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="banDialogVisible = false">取消</el-button>
          <el-button
              :type="selectuser.status === 1 ? 'success' : 'danger'"
              @click="submitBanAction"
          >
            <el-icon v-if="selectuser.status === 1"><Unlock /></el-icon>
            <el-icon v-else><Lock /></el-icon>
            {{ selectuser.status === 1 ? '确认解封' : '确认封禁' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.user-list-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;

  .header-card {
    margin-bottom: 20px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
        color: #303133;
        font-size: 24px;

        .el-icon {
          color: #409EFF;
        }
      }

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
  }

  .filter-card {
    margin-bottom: 20px;

    .filter-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .user-cell {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;

      .el-avatar {
        border: 2px solid #e4e7ed;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409EFF;
          transform: scale(1.05);
        }
      }

      .user-info {
        text-align: left;

        .username {
          font-weight: 600;
          color: #303133;
          margin-bottom: 2px;
          font-size: 14px;

          &:hover {
            color: #409EFF;
            cursor: pointer;
          }
        }

        .user-id {
          font-size: 12px;
          color: #909399;
          background-color: #f5f7fa;
          padding: 2px 6px;
          border-radius: 4px;
          display: inline-block;
        }
      }
    }

    .time-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 4px;
      gap: 8px;

      .time-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        transition: all 0.3s ease;

        &.recent-login {
          background: linear-gradient(135deg, #67C23A, #95d475);
          color: white;
        }

        &.week-login {
          background: linear-gradient(135deg, #409EFF, #66b1ff);
          color: white;
        }

        &.month-login {
          background: linear-gradient(135deg, #E6A23C, #f0c78a);
          color: white;
        }

        &.old-login {
          background: linear-gradient(135deg, #F56C6C, #f89898);
          color: white;
        }

        &.never-login {
          background: linear-gradient(135deg, #909399, #b1b3b8);
          color: white;
        }
      }

      .time-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .time-value {
          font-size: 13px;
          font-weight: 600;
          color: #303133;
          line-height: 1.2;

          &.no-login {
            color: #909399;
            font-style: italic;
          }
        }

        .time-detail {
          font-size: 11px;
          color: #909399;
          margin-top: 2px;

          &.inactive {
            color: #F56C6C;
            font-weight: 500;
          }
        }
      }

      &.register-time .time-icon {
        background: linear-gradient(135deg, #409EFF, #66b1ff);
        color: white;
      }

      &:hover {
        .time-icon {
          transform: scale(1.1);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }

    // 美化标签样式
    .el-tag {
      font-weight: 500;
      border-radius: 6px;
      padding: 4px 8px;

      &.el-tag--large {
        padding: 6px 12px;
        font-size: 13px;
      }

      &.el-tag--danger {
        background: linear-gradient(135deg, #F56C6C, #f89898);
        border: none;
        color: white;
      }

      &.el-tag--success {
        background: linear-gradient(135deg, #67C23A, #95d475);
        border: none;
        color: white;
      }

      &.el-tag--info {
        background: linear-gradient(135deg, #909399, #b1b3b8);
        border: none;
        color: white;
      }

      &.el-tag--warning {
        background: linear-gradient(135deg, #E6A23C, #f0c78a);
        border: none;
        color: white;
      }
    }

    .action-buttons {
      display: flex;
      gap: 6px;
      justify-content: flex-end;
      flex-wrap: wrap;
      padding: 4px 0;

      .el-button {
        transition: all 0.3s ease;
        border-radius: 6px;
        font-weight: 500;
        padding: 8px 12px;
        font-size: 13px;
        min-width: 60px;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &:active {
          transform: translateY(0);
        }

        .el-icon {
          margin-right: 4px;
        }

        &.el-button--primary {
          background: linear-gradient(135deg, #409EFF, #66b1ff);
          border: none;
          color: white;

          &:hover {
            background: linear-gradient(135deg, #66b1ff, #409EFF);
          }
        }

        &.el-button--warning {
          background: linear-gradient(135deg, #E6A23C, #f0c78a);
          border: none;
          color: white;

          &:hover {
            background: linear-gradient(135deg, #f0c78a, #E6A23C);
          }
        }

        &.el-button--danger {
          background: linear-gradient(135deg, #F56C6C, #f89898);
          border: none;
          color: white;

          &:hover {
            background: linear-gradient(135deg, #f89898, #F56C6C);
          }
        }

        &.el-button--success {
          background: linear-gradient(135deg, #67C23A, #95d475);
          border: none;
          color: white;

          &:hover {
            background: linear-gradient(135deg, #95d475, #67C23A);
          }
        }
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

// 表格行样式
:deep(.admin-row) {
  background-color: #fdf6ec;

  &:hover {
    background-color: #faecd8 !important;
  }
}

:deep(.banned-row) {
  background-color: #fef0f0;

  &:hover {
    background-color: #fde2e2 !important;
  }

  .el-table__cell {
    color: #909399;
  }
}

// 头像上传样式
.avatar-uploader {
  .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
      border-color: var(--el-color-primary);
    }
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    border-radius: 6px;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 对话框样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// 封禁对话框样式
.ban-dialog-content {
  .user-info-section {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .user-details {
      flex: 1;

      h3 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 18px;
      }

      p {
        margin: 0 0 8px 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .unban-section {
    .el-alert {
      margin-bottom: 0;
    }
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-alert {
    margin-top: 15px;

    p {
      margin: 2px 0;
      font-size: 13px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-list-container {
    padding: 10px;

    .header-content {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
    }

    .filter-form {
      .el-form-item {
        display: block;
        margin-bottom: 15px;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;

      .el-button {
        width: 100%;
        min-width: auto;
        padding: 6px 8px;
        font-size: 12px;
      }
    }

    .time-cell {
      flex-direction: column;
      gap: 4px;

      .time-icon {
        width: 24px;
        height: 24px;
        font-size: 12px;
      }

      .time-content {
        align-items: center;

        .time-value {
          font-size: 11px;
        }

        .time-detail {
          font-size: 10px;
        }
      }
    }
  }
}

// 权限提示样式
.permission-tip {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;

  .el-text {
    display: flex;
    align-items: center;
    gap: 4px;

    &::before {
      content: "ℹ️";
      font-size: 12px;
    }
  }
}

@media (max-width: 1200px) {
  .action-buttons {
    .el-button {
      padding: 6px 8px;
      font-size: 12px;
      min-width: 50px;
    }
  }
}
</style>
