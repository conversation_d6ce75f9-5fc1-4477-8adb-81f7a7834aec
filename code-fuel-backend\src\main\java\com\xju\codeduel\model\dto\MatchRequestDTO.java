package com.xju.codeduel.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 匹配请求数据传输对象
 * 
 * 功能说明：
 * 1. 存储用户的匹配请求信息
 * 2. 包含用户偏好和匹配状态
 * 3. 用于匹配算法的计算
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@ApiModel(value = "匹配请求信息", description = "用户匹配请求的详细信息")
public class MatchRequestDTO {

    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    private Long userId;

    @ApiModelProperty(value = "用户Codeforces ID", example = "tourist")
    private String codeforcesId;

    @ApiModelProperty(value = "用户当前Rating", example = "1500")
    private Integer userRating;

    @ApiModelProperty(value = "最小难度偏好", example = "800")
    private Integer minDifficulty;

    @ApiModelProperty(value = "最大难度偏好", example = "2000")
    private Integer maxDifficulty;

    @ApiModelProperty(value = "匹配开始时间")
    private LocalDateTime matchStartTime;

    @ApiModelProperty(value = "匹配状态", example = "WAITING", 
                     notes = "WAITING-等待中, MATCHED-已匹配, TIMEOUT-超时, CANCELLED-已取消")
    private String status;

    @ApiModelProperty(value = "等待时间（秒）", example = "30")
    private Long waitingTime;

    @ApiModelProperty(value = "匹配的房间码", example = "123456")
    private Long matchedRoomCode;

    @ApiModelProperty(value = "匹配成功的时间戳")
    private Long matchedTime;

    // 构造函数
    public MatchRequestDTO() {}

    public MatchRequestDTO(Long userId, String codeforcesId, Integer userRating, 
                          Integer minDifficulty, Integer maxDifficulty) {
        this.userId = userId;
        this.codeforcesId = codeforcesId;
        this.userRating = userRating;
        this.minDifficulty = minDifficulty;
        this.maxDifficulty = maxDifficulty;
        this.matchStartTime = LocalDateTime.now();
        this.status = "WAITING";
        this.waitingTime = 0L;
    }

    // Getter和Setter方法
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCodeforcesId() {
        return codeforcesId;
    }

    public void setCodeforcesId(String codeforcesId) {
        this.codeforcesId = codeforcesId;
    }

    public Integer getUserRating() {
        return userRating;
    }

    public void setUserRating(Integer userRating) {
        this.userRating = userRating;
    }

    public Integer getMinDifficulty() {
        return minDifficulty;
    }

    public void setMinDifficulty(Integer minDifficulty) {
        this.minDifficulty = minDifficulty;
    }

    public Integer getMaxDifficulty() {
        return maxDifficulty;
    }

    public void setMaxDifficulty(Integer maxDifficulty) {
        this.maxDifficulty = maxDifficulty;
    }

    public LocalDateTime getMatchStartTime() {
        return matchStartTime;
    }

    public void setMatchStartTime(LocalDateTime matchStartTime) {
        this.matchStartTime = matchStartTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getWaitingTime() {
        return waitingTime;
    }

    public void setWaitingTime(Long waitingTime) {
        this.waitingTime = waitingTime;
    }

    public Long getMatchedRoomCode() {
        return matchedRoomCode;
    }

    public void setMatchedRoomCode(Long matchedRoomCode) {
        this.matchedRoomCode = matchedRoomCode;
    }

    public Long getMatchedTime() {
        return matchedTime;
    }

    public void setMatchedTime(Long matchedTime) {
        this.matchedTime = matchedTime;
    }

    /**
     * 计算当前等待时间
     */
    public void updateWaitingTime() {
        if (matchStartTime != null) {
            this.waitingTime = java.time.Duration.between(matchStartTime, LocalDateTime.now()).getSeconds();
        }
    }

    /**
     * 检查是否与另一个匹配请求兼容
     */
    public boolean isCompatibleWith(MatchRequestDTO other) {
        if (other == null) return false;

        // Rating差距检查（基础差距400分，等待时间越长越宽松）
        int ratingDiff = Math.abs(this.userRating - other.userRating);
        long totalWaitingTime = Math.max(this.waitingTime, other.waitingTime);
        int maxRatingDiff = 400 + (int)(totalWaitingTime / 15) * 100; // 每15秒放宽100分

        System.out.println("🔍 兼容性检查: " + this.codeforcesId + " vs " + other.codeforcesId);
        System.out.println("   Rating差距: " + ratingDiff + ", 允许差距: " + maxRatingDiff);
        System.out.println("   等待时间: " + this.waitingTime + "s vs " + other.waitingTime + "s");

        if (ratingDiff > maxRatingDiff) {
            System.out.println("   ❌ Rating差距过大");
            return false;
        }

        // 难度偏好重叠检查
        int overlapMin = Math.max(this.minDifficulty, other.minDifficulty);
        int overlapMax = Math.min(this.maxDifficulty, other.maxDifficulty);

        System.out.println("   难度重叠: [" + overlapMin + ", " + overlapMax + "]");

        boolean compatible = overlapMin <= overlapMax;
        System.out.println("   结果: " + (compatible ? "✅ 兼容" : "❌ 不兼容"));

        return compatible;
    }

    @Override
    public String toString() {
        return "MatchRequestDTO{" +
                "userId=" + userId +
                ", codeforcesId='" + codeforcesId + '\'' +
                ", userRating=" + userRating +
                ", minDifficulty=" + minDifficulty +
                ", maxDifficulty=" + maxDifficulty +
                ", status='" + status + '\'' +
                ", waitingTime=" + waitingTime +
                '}';
    }
}
