package com.xju.codeduel.event;

import com.xju.codeduel.service.IMatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 对战结束事件监听器
 * 
 * 监听对战结束事件，执行相关的清理工作
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Component
public class BattleEndEventListener {

    private static final Logger logger = LoggerFactory.getLogger(BattleEndEventListener.class);

    @Autowired
    private IMatchService matchService;

    /**
     * 处理对战结束事件
     * 
     * @param event 对战结束事件
     */
    @EventListener
    @Async
    public void handleBattleEndEvent(BattleEndEvent event) {
        logger.info("🎧 收到对战结束事件，房间: {}, 参与者: {}", event.getRoomCode(), event.getParticipantIds());

        // 清理所有参与者的匹配队列记录
        for (Long participantId : event.getParticipantIds()) {
            try {
                matchService.forceRemoveFromQueue(participantId);
                logger.info("🧹 已清理用户 {} 的匹配队列记录", participantId);
            } catch (Exception e) {
                logger.warn("⚠️ 清理用户 {} 匹配队列记录时出现异常: {}", participantId, e.getMessage());
            }
        }

        logger.info("✅ 对战结束事件处理完成，房间: {}", event.getRoomCode());
    }
}
